# Use Node.js as base image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy root level configuration files
COPY package*.json ./
COPY angular.json ./
COPY tsconfig.json ./
COPY staticwebapp.config.json ./
COPY play-plus.tgz ./

# Copy project source code
COPY projects/experience-studio ./projects/experience-studio
COPY projects/shared ./projects/shared

# Install dependencies
RUN npm ci

# Expose port
EXPOSE 4201

# Start development server
CMD ["npm", "run", "start:experience-studio"]
