# Use Node.js as base image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY angular.json ./
COPY tsconfig.json ./
COPY staticwebapp.config.json ./
COPY .npmrc ./
COPY play-plus.tgz ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Expose port
EXPOSE 4201

# Start development server
CMD ["npm", "run", "start:experience-studio"]
