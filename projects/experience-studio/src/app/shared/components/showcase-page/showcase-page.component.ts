import { Component, ChangeDetectionStrategy, inject, signal, computed, OnInit, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WorkspaceStateService, CommunityProject } from '../../services/workspace-state.service';
import { CommunityProjectsService } from '../../services/community-projects.service';
import { ShowcaseFiltersComponent } from '../showcase-filters/showcase-filters.component';
import { CommunityProjectCardComponent } from '../community-project-card/community-project-card.component';
import { PaginationComponent } from '../pagination/pagination.component';

/**
 * Showcase Page Component - Community projects discovery and exploration
 * Implements the three-column layout with filters sidebar, projects grid, and activity feed
 * 
 * Features:
 * - Advanced filtering and search
 * - Responsive grid layout
 * - Infinite scroll/pagination
 * - Project detail modals
 * - Category-based browsing
 * - Sort and view options
 */
@Component({
  selector: 'app-showcase-page',
  standalone: true,
  imports: [
    CommonModule,
    ShowcaseFiltersComponent,
    CommunityProjectCardComponent,
    PaginationComponent
  ],
  template: `
    <div class="showcase-page">
      <!-- Showcase Header -->
      <div class="showcase-header">
        <div class="header-content">
          <div class="page-title">
            <h1>Community Showcase</h1>
            <p class="page-subtitle">
              Discover amazing projects created by our community
            </p>
          </div>
          
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-number">{{ totalResults() }}</span>
              <span class="stat-label">Projects</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ totalCreators() }}</span>
              <span class="stat-label">Creators</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ totalCategories() }}</span>
              <span class="stat-label">Categories</span>
            </div>
          </div>
        </div>
        
        <!-- Category Tabs -->
        <div class="category-tabs">
          @for (category of categories(); track category.id) {
            <button
              class="category-tab"
              [class.active]="activeCategory() === category.id"
              (click)="selectCategory(category.id)">
              <i [class]="category.icon" aria-hidden="true"></i>
              <span>{{ category.name }}</span>
              <span class="category-count">({{ category.count }})</span>
            </button>
          }
        </div>
      </div>
      
      <!-- Main Content -->
      <div class="showcase-content">
        <!-- Filters Sidebar -->
        <aside class="filters-sidebar" [class.collapsed]="!showFilters()">
          <div class="sidebar-header">
            <h3>Filters</h3>
            <button 
              class="toggle-filters"
              (click)="toggleFilters()"
              [attr.aria-label]="showFilters() ? 'Hide filters' : 'Show filters'">
              <i [class]="showFilters() ? 'fas fa-chevron-left' : 'fas fa-chevron-right'" aria-hidden="true"></i>
            </button>
          </div>
          
          @if (showFilters()) {
            <app-showcase-filters
              [filters]="activeFilters()"
              [availableFilters]="availableFilters()"
              (filterChange)="onFilterChange($event)"
              (clearFilters)="onClearFilters()">
            </app-showcase-filters>
          }
        </aside>
        
        <!-- Projects Grid -->
        <main class="projects-main">
          <!-- Results Header -->
          <div class="results-header">
            <div class="results-info">
              <span class="results-count">
                @if (isLoading()) {
                  <i class="fas fa-spinner fa-spin" aria-hidden="true"></i>
                  Loading...
                } @else {
                  {{ filteredProjects().length }} of {{ totalResults() }} projects
                }
              </span>
              
              @if (hasActiveFilters()) {
                <button 
                  class="clear-filters-btn"
                  (click)="onClearFilters()">
                  <i class="fas fa-times" aria-hidden="true"></i>
                  Clear filters
                </button>
              }
            </div>
            
            <div class="results-controls">
              <!-- View Toggle -->
              <div class="view-toggle">
                <button
                  class="view-btn"
                  [class.active]="currentView() === 'grid'"
                  (click)="setView('grid')"
                  aria-label="Grid view">
                  <i class="fas fa-th" aria-hidden="true"></i>
                </button>
                <button
                  class="view-btn"
                  [class.active]="currentView() === 'list'"
                  (click)="setView('list')"
                  aria-label="List view">
                  <i class="fas fa-list" aria-hidden="true"></i>
                </button>
              </div>
              
              <!-- Sort Controls -->
              <div class="sort-controls">
                <select 
                  class="sort-select"
                  [value]="sortBy()"
                  (change)="onSortChange($event)"
                  aria-label="Sort projects by">
                  <option value="relevance">Most Relevant</option>
                  <option value="date">Newest First</option>
                  <option value="rating">Highest Rated</option>
                  <option value="views">Most Viewed</option>
                  <option value="name">Name A-Z</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Projects Grid -->
          <div 
            class="projects-grid" 
            [attr.data-view]="currentView()"
            [attr.data-loading]="isLoading()">
            
            @if (isLoading() && filteredProjects().length === 0) {
              <!-- Loading Skeleton -->
              @for (item of loadingItems(); track $index) {
                <div class="project-skeleton">
                  <div class="skeleton-image"></div>
                  <div class="skeleton-content">
                    <div class="skeleton-title"></div>
                    <div class="skeleton-description"></div>
                    <div class="skeleton-meta"></div>
                  </div>
                </div>
              }
            } @else {
              @for (project of paginatedProjects(); track project.id) {
                <app-community-project-card
                  [project]="project"
                  [view]="currentView()"
                  (select)="onProjectSelect(project)"
                  (favorite)="onProjectFavorite(project)"
                  (share)="onProjectShare(project)">
                </app-community-project-card>
              } @empty {
                <div class="no-results">
                  <div class="no-results-icon">
                    <i class="fas fa-search" aria-hidden="true"></i>
                  </div>
                  <h3 class="no-results-title">No projects found</h3>
                  <p class="no-results-description">
                    Try adjusting your search terms or filters to find what you're looking for.
                  </p>
                  <button 
                    class="clear-filters-btn primary"
                    (click)="onClearFilters()">
                    Clear all filters
                  </button>
                </div>
              }
            }
          </div>
          
          <!-- Pagination -->
          @if (totalPages() > 1) {
            <app-pagination
              [currentPage]="currentPage()"
              [totalPages]="totalPages()"
              [totalItems]="filteredProjects().length"
              [itemsPerPage]="itemsPerPage()"
              (pageChange)="onPageChange($event)">
            </app-pagination>
          }
        </main>
        
        <!-- Activity Feed (Right Sidebar) -->
        <aside class="activity-sidebar">
          <div class="sidebar-section">
            <h3>Trending Now</h3>
            <div class="trending-list">
              @for (trend of trendingProjects(); track trend.id) {
                <div class="trending-item" (click)="onProjectSelect(trend)">
                  <div class="trending-rank">{{ $index + 1 }}</div>
                  <div class="trending-info">
                    <span class="trending-title">{{ trend.title }}</span>
                    <span class="trending-meta">{{ trend.views }} views</span>
                  </div>
                </div>
              }
            </div>
          </div>
          
          <div class="sidebar-section">
            <h3>Featured Creators</h3>
            <div class="creators-list">
              @for (creator of featuredCreators(); track creator.id) {
                <div class="creator-item">
                  <img [src]="creator.avatar" [alt]="creator.name" class="creator-avatar">
                  <div class="creator-info">
                    <span class="creator-name">{{ creator.name }}</span>
                    <span class="creator-projects">{{ creator.projectCount }} projects</span>
                  </div>
                </div>
              }
            </div>
          </div>
        </aside>
      </div>
    </div>
  `,
  styleUrls: ['./showcase-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ShowcasePageComponent implements OnInit {
  private readonly workspaceState = inject(WorkspaceStateService);
  private readonly communityService = inject(CommunityProjectsService);

  // Component state
  private readonly _showFilters = signal<boolean>(true);
  private readonly _currentView = signal<'grid' | 'list'>('grid');
  private readonly _activeCategory = signal<string>('all');
  private readonly _itemsPerPage = signal<number>(12);

  // Public readonly signals from workspace state
  readonly activeFilters = this.workspaceState.activeFilters;
  readonly isLoading = this.workspaceState.isLoading;
  readonly totalResults = this.workspaceState.totalResults;
  readonly currentPage = this.workspaceState.currentPage;
  readonly filteredProjects = this.workspaceState.filteredProjects;
  readonly searchQuery = this.workspaceState.searchQuery;

  // Component signals
  readonly showFilters = this._showFilters.asReadonly();
  readonly currentView = this._currentView.asReadonly();
  readonly activeCategory = this._activeCategory.asReadonly();
  readonly itemsPerPage = this._itemsPerPage.asReadonly();

  // Service data
  readonly categories = this.communityService.categories;
  readonly availableFilters = this.communityService.availableFilters;
  readonly trendingProjects = this.communityService.trendingProjects;
  readonly featuredCreators = this.communityService.featuredCreators;

  // Computed properties
  readonly hasActiveFilters = this.workspaceState.hasActiveFilters;
  readonly sortBy = computed(() => this.activeFilters().sortBy);
  
  readonly totalPages = computed(() => 
    Math.ceil(this.filteredProjects().length / this._itemsPerPage())
  );
  
  readonly paginatedProjects = computed(() => {
    const projects = this.filteredProjects();
    const page = this.currentPage();
    const perPage = this._itemsPerPage();
    const start = (page - 1) * perPage;
    return projects.slice(start, start + perPage);
  });

  readonly loadingItems = computed(() => Array(12).fill(null));
  
  readonly totalCreators = computed(() => this.featuredCreators().length);
  readonly totalCategories = computed(() => this.categories().length);

  constructor() {
    // Watch for search query changes and trigger API calls
    effect(() => {
      const query = this.searchQuery();
      if (query && query.trim().length > 2) {
        this.performSearch(query);
      } else if (!query) {
        // Reload default data when search is cleared
        this.loadShowcaseData();
      }
    });
  }

  ngOnInit(): void {
    this.loadShowcaseData();
  }

  /**
   * Load showcase data with API integration
   */
  private loadShowcaseData(): void {
    // Load community projects with default query
    this.communityService.loadCommunityProjects({
      scope: 'community',
      limit: 20,
      sort_by: 'date',
      sort_order: 'desc'
    });

    this.communityService.loadCategories();
    this.communityService.loadTrendingProjects();
    this.communityService.loadFeaturedCreators();
    this.communityService.loadAvailableFilters();
  }

  /**
   * Perform search with current filters
   */
  private performSearch(query: string): void {
    const currentFilters = this.workspaceState.activeFilters();

    this.communityService.loadCommunityProjects({
      scope: 'community',
      search: query,
      category: this._activeCategory() === 'all' ? undefined : this._activeCategory(),
      project_types: currentFilters.projectTypes,
      technologies: currentFilters.technologies,
      difficulty: currentFilters.difficulty,
      creator: currentFilters.creator,
      sort_by: currentFilters.sortBy,
      sort_order: currentFilters.sortOrder,
      limit: 20
    });
  }

  /**
   * Toggle filters sidebar
   */
  toggleFilters(): void {
    this._showFilters.set(!this._showFilters());
  }

  /**
   * Select category and reload data
   */
  selectCategory(categoryId: string): void {
    this._activeCategory.set(categoryId);
    this.workspaceState.updateFilters({
      projectTypes: categoryId === 'all' ? [] : [categoryId]
    });

    // Reload projects with new category filter
    this.communityService.loadCommunityProjects({
      scope: 'community',
      category: categoryId === 'all' ? undefined : categoryId,
      limit: 20,
      sort_by: this.sortBy(),
      sort_order: 'desc'
    });
  }

  /**
   * Set view mode
   */
  setView(view: 'grid' | 'list'): void {
    this._currentView.set(view);
  }

  /**
   * Handle filter changes and reload data
   */
  onFilterChange(filters: any): void {
    this.workspaceState.updateFilters(filters);
    this.workspaceState.setCurrentPage(1); // Reset to first page

    // Reload projects with new filters
    const currentFilters = this.workspaceState.activeFilters();
    this.communityService.loadCommunityProjects({
      scope: 'community',
      category: this._activeCategory() === 'all' ? undefined : this._activeCategory(),
      project_types: currentFilters.projectTypes,
      technologies: currentFilters.technologies,
      difficulty: currentFilters.difficulty,
      creator: currentFilters.creator,
      sort_by: currentFilters.sortBy,
      sort_order: currentFilters.sortOrder,
      limit: 20
    });
  }

  /**
   * Clear all filters
   */
  onClearFilters(): void {
    this.workspaceState.clearAllFilters();
    this._activeCategory.set('all');
  }

  /**
   * Handle sort change
   */
  onSortChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.workspaceState.updateFilters({ sortBy: target.value as any });
  }

  /**
   * Handle page change
   */
  onPageChange(page: number): void {
    this.workspaceState.setCurrentPage(page);
    // Scroll to top of results
    document.querySelector('.projects-main')?.scrollIntoView({ behavior: 'smooth' });
  }

  /**
   * Handle project selection
   */
  onProjectSelect(project: CommunityProject): void {
    // Open project detail modal or navigate to project page
    console.log('Selected project:', project);
  }

  /**
   * Handle project favorite
   */
  onProjectFavorite(project: CommunityProject): void {
    // Toggle favorite status
    console.log('Favorited project:', project);
  }

  /**
   * Handle project share
   */
  onProjectShare(project: CommunityProject): void {
    // Open share modal or copy link
    console.log('Shared project:', project);
  }
}
