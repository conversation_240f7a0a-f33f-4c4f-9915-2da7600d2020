<!-- Split-screen layout: Chat window on left, Infinite canvas on right -->
<awe-splitscreen
  class="ui-design-container smooth-split-screen"
  [isResizable]="true"
  [minWidth]="'300'"
  defaultLeftPanelWidth="35%"
  defaultRightPanelWidth="65%">

  <!-- Left Panel: Chat Window -->
  <awe-leftpanel [hasHeader]="true" awe-leftpanel>
    <div awe-leftpanel-header>
      <!-- Custom left panel header -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <awe-icons
            iconName="awe_home"
            iconColor="neutralIcon"
            class="cursor-pointer"
            (click)="navigateToHome()"
            title="Navigate to home page"></awe-icons>
        </div>
        <div class="header-center">
          <div class="project-name">
            UI Design Preview
          </div>
        </div>
        <div class="header-right">
          <!-- Empty for now -->
        </div>
      </div>
    </div>

    <div awe-leftpanel-content class="adjust-height">
      <!-- Loading state -->
      <div *ngIf="isLoading | async" class="loading-container">
        <app-loading-animation
          [theme]="(currentTheme | async) || 'light'"
          [messages]="loadingMessages">
        </app-loading-animation>
      </div>

      <!-- Chat window when not loading -->
      <div *ngIf="!(isLoading | async)" class="chat-container adjust-height">
        <app-chat-window
          #chatWindow
          [theme]="(currentTheme | async) || 'light'"
          [defaultText]="'Ask me to modify the design'"
          [rightIcons]="rightIcons"
          [(textValue)]="lightPrompt"
          [chatMessages]="lightMessages"
          [showStepper]="false"
          [isCodeGenerationComplete]="true"
          [useApi]="false"
          (iconClicked)="handleIconClick($event)"
          (enterPressed)="handleEnhancedSendLight()">
        </app-chat-window>
      </div>
    </div>
  </awe-leftpanel>

  <!-- Right Panel: Infinite Canvas -->
  <awe-rightpanel [hasHeader]="true" awe-rightpanel>
    <div awe-rightpanel-header>
      <!-- Custom right panel header -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <div class="header-title">
            <span>UI Design Canvas</span>
          </div>
        </div>
        <div class="header-right">
          <div class="canvas-info" *ngIf="!(isLoading | async)">
            <span>{{ (nodes$ | async)?.length || 0 }} screens</span>
          </div>
        </div>
      </div>
    </div>

    <div awe-rightpanel-content>
      <!-- Wireframe Generation Loading Overlay -->
      <div *ngIf="wireframeGenerationState.isGenerating" class="wireframe-loading-overlay">
        <div class="wireframe-loading-content">
          <div class="container">
            <div class="loader"></div>
            <div class="loader"></div>
            <div class="loader"></div>
          </div>
        </div>
      </div>

      <!-- Loading state for canvas -->
      <div *ngIf="isLoading | async" class="canvas-loading-container">
        <app-loading-animation
          [theme]="(currentTheme | async) || 'light'"
          [messages]="loadingMessages">
        </app-loading-animation>
      </div>

      <!-- Error state -->
      <div *ngIf="hasError | async" class="error-container">
        <div class="error-content">
          <i class="bi bi-exclamation-triangle error-icon"></i>
          <h3>Generation Failed</h3>
          <p>{{ errorMessage | async }}</p>
          <button class="retry-button" (click)="loadUIDesignData()">
            <i class="bi bi-arrow-clockwise"></i>
            Try Again
          </button>
        </div>
      </div>

      <!-- Debug Info -->
      <div class="debug-info" style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 4px; font-size: 12px; z-index: 1000;">
        <div>Nodes: {{ (nodes$ | async)?.length || 0 }}</div>
        <div>UI Design Nodes: {{ getUIDesignNodesCount() }}</div>
        <div>Local Nodes: {{ getLocalNodesCount() }}</div>
        <div>Backup Nodes: {{ getBackupNodesCount() }}</div>
        <div>Loading: {{ isLoading | async }}</div>
        <div>Error: {{ hasError | async }}</div>
        <div>Zoom: {{ getZoomPercentage() }}%</div>
        <div>Canvas Transform: {{ getCanvasTransformStyle() }}</div>
      </div>

      <!-- React Flow-Inspired Infinite Canvas -->
      <div
        *ngIf="!(isLoading | async) && !(hasError | async)"
        #canvasContainer
        class="react-flow-canvas"
        [ngClass]="(currentTheme | async) + '-theme'"
        (mousedown)="onCanvasMouseDown($event)">

        <!-- Canvas Controls -->
        <div class="canvas-controls">
          <div class="zoom-controls">
            <button
              class="control-button"
              (click)="zoomOut()"
              [disabled]="(canvasViewport$ | async)?.zoom! <= 0.1"
              title="Zoom Out">
              <i class="bi bi-dash"></i>
            </button>

            <div class="zoom-level">
              {{ getZoomPercentage() }}%
            </div>

            <button
              class="control-button"
              (click)="zoomIn()"
              [disabled]="(canvasViewport$ | async)?.zoom! >= 3"
              title="Zoom In">
              <i class="bi bi-plus"></i>
            </button>
          </div>

          <div class="view-controls">
            <button
              class="control-button"
              (click)="resetCanvasView()"
              title="Reset View">
              <i class="bi bi-house"></i>
            </button>

            <button
              class="control-button"
              (click)="fitToView()"
              title="Fit to View">
              <i class="bi bi-arrows-angle-expand"></i>
            </button>

            <button
              class="control-button"
              (click)="toggleMiniMap()"
              [class.active]="(minimapConfig$ | async)?.enabled"
              title="Toggle Mini Map">
              <i class="bi bi-map"></i>
            </button>
          </div>
        </div>

        <!-- Canvas Content with Transform -->
        <div
          class="canvas-content"
          [style.transform]="getCanvasTransformStyle()"
          style="min-height: 100%; background: rgba(255,0,0,0.1);">

          <!-- Screen Nodes (React Flow inspired) -->
          <div
            *ngFor="let node of (nodes$ | async); trackBy: trackByNode"
            class="screen-node"
            [class.selected]="node.selected"
            [class.dragging]="node.dragging"
            [class.loading]="node.data.isLoading"
            [style.left.px]="node.position.x"
            [style.top.px]="node.position.y"
            [style.width.px]="node.data.width"
            [style.height.px]="node.data.height"
            [attr.data-node-id]="node.id"
            (click)="onNodeClick(node, $event)"
            (dblclick)="onNodeDoubleClick(node)"
            [title]="'Double-click to view ' + node.data.title + ' in full-screen'">

            <div class="node-header">
              <h4>{{ node.data.title }}</h4>
              <div class="node-actions">
                <button
                  class="node-action-button download-button"
                  (click)="onNodeDownloadClick(node, $event)"
                  title="Download as PNG">
                  <svg width="14" height="14" viewBox="0 0 16 16" fill="none">
                    <path d="M8.5 1a.5.5 0 0 0-1 0v5.793L5.354 4.646a.5.5 0 1 0-.708.708l3 3a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 6.793V1z" fill="currentColor"/>
                    <path d="M3 9.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z" fill="currentColor"/>
                    <path d="M2.5 12a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5h-11z" fill="currentColor"/>
                  </svg>
                </button>
                <i class="bi bi-arrows-fullscreen expand-icon"></i>
              </div>
            </div>

            <div class="node-content">
              <div class="node-preview">
                <iframe
                  [safeSrcdoc]="node.data.rawContent"
                  class="preview-iframe"
                  frameborder="0"
                  sandbox="allow-same-origin allow-scripts allow-top-navigation-by-user-activation"
                  loading="lazy">
                </iframe>
              </div>
            </div>

            <div class="node-overlay" *ngIf="!node.selected">
              <div class="overlay-content">
                <i class="bi bi-arrows-fullscreen"></i>
                <span>Double-click to expand</span>
              </div>
            </div>
          </div>

          <!-- Test Node (Static) -->
          <div class="screen-node test-node" style="left: 100px; top: 100px; width: 300px; height: 200px; position: absolute; background: white; border: 2px solid #3b82f6; border-radius: 12px;">
            <div class="node-header" style="padding: 12px 16px; border-bottom: 1px solid #e5e7eb; background: white;">
              <h4 style="margin: 0; font-size: 14px;">Test Node</h4>
            </div>
            <div class="node-content" style="height: calc(100% - 44px); padding: 16px; display: flex; align-items: center; justify-content: center;">
              <p>This is a test node to verify rendering</p>
            </div>
          </div>

          <!-- Fallback message if no nodes -->
          <div *ngIf="(nodes$ | async)?.length === 0" class="no-nodes-message">
            <div class="message-content">
              <i class="bi bi-grid-3x3-gap"></i>
              <h3>No UI Designs Available</h3>
              <p>UI design nodes will appear here once generated.</p>
              <p>Service nodes count: {{ (nodes$ | async)?.length || 'undefined' }}</p>
            </div>
          </div>
        </div>

        <!-- Mini Map -->
        <div
          *ngIf="(minimapConfig$ | async)?.enabled"
          class="mini-map">
          <div class="mini-map-content">
            <div class="mini-map-header">
              <span>Overview</span>
              <button
                class="mini-map-close"
                (click)="toggleMiniMap()">
                <i class="bi bi-x"></i>
              </button>
            </div>
            <div class="mini-map-canvas">
              <div
                *ngFor="let node of (nodes$ | async)"
                class="mini-node"
                [style.left.px]="node.position.x / 10"
                [style.top.px]="node.position.y / 10"
                [style.width.px]="node.data.width / 10"
                [style.height.px]="node.data.height / 10"
                [class.selected]="node.selected">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </awe-rightpanel>
</awe-splitscreen>

<!-- Full-screen Modal -->
<div
  *ngIf="isFullScreenOpen | async"
  #fullScreenModal
  class="fullscreen-modal"
  [ngClass]="(currentTheme | async) + '-theme'"
  (click)="closeFullScreen()">

  <div class="modal-content" (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="modal-header">
      <div class="modal-title">
        <h3>{{ (selectedScreenNode | async)?.data?.title }}</h3>
      </div>
      <div class="modal-actions">
        <button
          class="action-button download-button"
          (click)="downloadSelectedNodeScreenshot()"
          [disabled]="!(selectedScreenNode | async)"
          title="Download Screenshot">
          <i class="bi bi-download"></i>
        </button>
        <button
          class="action-button new-tab-button"
          (click)="openSelectedNodeInNewTab()"
          [disabled]="!(selectedScreenNode | async)"
          title="Open in New Tab">
          <i class="bi bi-box-arrow-up-right"></i>
        </button>
        <button
          class="close-button"
          (click)="closeFullScreen()"
          title="Close (Esc)">
          <i class="bi bi-x-lg"></i>
        </button>
      </div>
    </div>

    <!-- View Mode Toggle -->
    <div class="view-mode-toggle">
      <button
        class="view-mode-button"
        [class.active]="(currentViewMode | async) === 'mobile'"
        (click)="switchViewMode('mobile')"
        title="Mobile View (375px)">
        <i class="bi bi-phone"></i>
        <span>Mobile</span>
      </button>
      <button
        class="view-mode-button"
        [class.active]="(currentViewMode | async) === 'web'"
        (click)="switchViewMode('web')"
        title="Desktop View (1200px)">
        <i class="bi bi-display"></i>
        <span>Desktop</span>
      </button>
    </div>

    <!-- Modal Content -->
    <div class="modal-body">
      <div
        class="fullscreen-preview"
        [class.mobile-view]="(currentViewMode | async) === 'mobile'"
        [class.web-view]="(currentViewMode | async) === 'web'">

        <iframe
          *ngIf="selectedScreenNode | async"
          [safeSrcdoc]="(selectedScreenNode | async)?.data?.rawContent || ''"
          class="fullscreen-iframe"
          frameborder="0"
          sandbox="allow-same-origin allow-scripts allow-top-navigation-by-user-activation">
        </iframe>
      </div>
    </div>
  </div>
</div>
