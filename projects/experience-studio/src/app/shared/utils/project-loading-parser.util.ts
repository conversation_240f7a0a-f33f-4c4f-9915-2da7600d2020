import {
  ProjectLoadingResponse,
  ConversationMessage
} from '../services/project-loading.service';
import {
  ParsedProjectData,
  ParsedConversationMessage,
  ChatWindowMessage,
  ChatCodeFile,
  StepperInitializationData,
  StepperStep,
  VersionAccordionData,
  VersionFile,
  CodeWindowInitializationData,
  RepositoryIntegrationData,
  FileData,
  FirstCutMetadata,
  ProjectLoadingError,
  UIDesignConversationData
} from '../interfaces/project-loading.interfaces';
import { UIDesignPage } from '../services/generate-ui-design.service';

/**
 * Utility class for parsing project loading data
 */
export class ProjectLoadingParser {

  /**
   * Parse SSE events from the metadata field (for first generation)
   * @param metadataString JSON string containing SSE events
   * @returns Array of parsed SSE events
   */
  static parseSSEEventsFromMetadata(metadataString: string): Array<{
    log: string;
    status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    progress: string;
    progress_description: string;
    metadata: Array<{ type: string; data: any }>;
    event_type?: string;
    project_id?: string;
    is_final?: boolean;
  }> {
    try {
      const metadataArray = JSON.parse(metadataString);
      return metadataArray.map((event: any) => ({
        log: event.log || '',
        status: event.status || 'COMPLETED',
        progress: event.progress || 'UNKNOWN',
        progress_description: event.progress_description || '',
        metadata: event.metadata || [],
        event_type: event.event_type,
        project_id: event.project_id,
        is_final: event.is_final || false
      }));
    } catch (error) {
      console.error('Failed to parse SSE metadata:', error);
      return [];
    }
  }

  /**
   * Extract code files from metadata SSE events (matching first generation flow)
   * Looks through all events for files metadata, prioritizing later events
   * @param metadataString JSON string containing SSE events
   * @returns Array of file data for code viewer
   */
  static extractCodeFilesFromMetadata(metadataString: string): FileData[] {
    try {
      const sseEvents = this.parseSSEEventsFromMetadata(metadataString);
      const allFiles: FileData[] = [];

      // Go through all SSE events and collect files from their metadata
      sseEvents.forEach(event => {
        if (event.metadata && event.metadata.length > 0) {
          event.metadata.forEach(meta => {
            // Look for files metadata in any event
            if (meta.type === 'files' && meta.data && Array.isArray(meta.data)) {
              const files = this.parseFilesFromMetadata(meta.data);
              allFiles.push(...files);
            }
          });
        }
      });

      // Remove duplicates based on fileName (later files override earlier ones)
      const uniqueFiles = new Map<string, FileData>();
      allFiles.forEach(file => {
        uniqueFiles.set(file.fileName, file);
      });

      const result = Array.from(uniqueFiles.values());
      console.log(`Extracted ${result.length} code files from metadata`);

      return result;

    } catch (error) {
      console.error('Failed to extract code files from metadata:', error);
      return [];
    }
  }

  /**
   * Parse files from metadata data array (same logic as app generation)
   * @param filesData Array of file objects from metadata
   * @returns Array of parsed file data
   */
  static parseFilesFromMetadata(filesData: any[]): FileData[] {
    if (!Array.isArray(filesData)) {
      console.warn('Files data is not an array:', filesData);
      return [];
    }

    const files: FileData[] = [];

    filesData.forEach((file: any) => {
      if (file.content && file.fileName) {
        files.push({
          fileName: file.fileName,
          content: file.content,
          language: this.getLanguageFromFileName(file.fileName),
          path: file.fileName
        });
      }
    });

    return files;
  }

  /**
   * Extract artifacts from metadata SSE events (matching exact SSE data structure)
   * Only extracts proper artifacts (text and json types) for clean display
   * @param metadataString JSON string containing SSE events
   * @returns Array of artifact data for artifacts viewer
   */
  static extractArtifactsFromMetadata(metadataString: string): Array<{
    name: string;
    type: 'text' | 'json' | 'markdown' | 'image' | 'component';
    content: any;
    source: string; // Which SSE event this came from
    progress: string; // Progress stage when artifact was created
    log: string; // Full log entry
    tokens?: any; // For design system artifacts
    layoutKey?: string; // For layout artifacts
    imageUrl?: string; // For layout artifacts
  }> {
    try {
      const sseEvents = this.parseSSEEventsFromMetadata(metadataString);
      const artifacts: Array<{
        name: string;
        type: 'text' | 'json' | 'markdown' | 'image' | 'component';
        content: any;
        source: string;
        progress: string;
        log: string;
        tokens?: any;
        layoutKey?: string;
        imageUrl?: string;
      }> = [];

      // Go through each SSE event and extract ONLY proper artifacts from their metadata
      sseEvents.forEach(event => {
        if (event.metadata && event.metadata.length > 0) {
          event.metadata.forEach(meta => {
            console.log('🔍 Processing metadata item:', { type: meta.type, hasData: !!meta.data });

            // Handle nested artifacts structure (type: "artifacts" with nested data)
            if (meta.type === 'artifacts' && meta.data) {
              console.log('🎨 Found artifacts metadata:', meta.data);

              // Extract text artifacts (Project Overview, Layout Analysis, etc.)
              if (meta.data.type === 'text') {
                const artifactName = event.progress === 'OVERVIEW'
                  ? 'Project Overview'
                  : event.progress === 'LAYOUT_ANALYZED'
                  ? 'Layout Analyzed'
                  : this.generateArtifactName(event.progress, 'text');

                // Format content properly for display
                const formattedContent = this.formatTextArtifactContent(meta.data.data, event.progress);

                artifacts.push({
                  name: artifactName,
                  type: event.progress === 'LAYOUT_ANALYZED' ? 'image' : 'markdown', // Layout should be image type
                  content: formattedContent,
                  source: event.log || event.progress_description || 'Unknown',
                  progress: event.progress,
                  log: event.log || '',
                  // Add layout-specific properties for Layout Analyzed
                  ...(event.progress === 'LAYOUT_ANALYZED' && {
                    layoutKey: this.extractLayoutKeyFromMetadata(
                      JSON.stringify(event.metadata || []),
                      event.progress,
                      event.status
                    ),
                    imageUrl: this.getLayoutImageUrl(meta.data.data)
                  })
                });

                // Debug layout artifact creation
                if (event.progress === 'LAYOUT_ANALYZED') {
                  const extractedLayoutKey = this.extractLayoutKeyFromMetadata(
                    JSON.stringify(event.metadata || []),
                    event.progress,
                    event.status
                  );
                  console.log('🎨 Created layout artifact:', {
                    name: artifactName,
                    progress: event.progress,
                    status: event.status,
                    layoutKey: extractedLayoutKey,
                    rawMetadata: event.metadata,
                    content: meta.data.data
                  });
                }

                console.log('✅ Added text artifact:', artifactName);
              }
              // Extract Design System JSON from nested artifacts
              else if (meta.data.type === 'json') {
                console.log('🎨 Processing design system JSON:', meta.data.data);
                const designTokens = this.formatDesignTokens(meta.data.data);

                artifacts.push({
                  name: 'Design System',
                  type: 'component', // Use component type for design system
                  content: 'Design system tokens and components',
                  tokens: designTokens, // Add tokens property for design system display
                  source: event.log || event.progress_description || 'Unknown',
                  progress: event.progress,
                  log: event.log || ''
                });

                console.log('✅ Added design system artifact with tokens:', designTokens);
              }
            }
            // Fallback: Handle old structure (type: "artifact" with nested data)
            else if (meta.type === 'artifact' && meta.data) {
              console.log('🔄 Processing fallback artifact structure:', meta.data);

              if (meta.data.type === 'text') {
                const artifactName = event.progress === 'OVERVIEW'
                  ? 'Project Overview'
                  : event.progress === 'LAYOUT_ANALYZED'
                  ? 'Layout Analyzed'
                  : this.generateArtifactName(event.progress, 'text');

                const formattedContent = this.formatTextArtifactContent(meta.data.data, event.progress);

                const layoutKey = event.progress === 'LAYOUT_ANALYZED'
                  ? this.extractLayoutKeyFromMetadata(
                      JSON.stringify(event.metadata || []),
                      event.progress,
                      event.status
                    )
                  : undefined;
                const imageUrl = event.progress === 'LAYOUT_ANALYZED' ? this.getLayoutImageUrl(meta.data.data) : undefined;

                artifacts.push({
                  name: artifactName,
                  type: event.progress === 'LAYOUT_ANALYZED' ? 'image' : 'markdown',
                  content: formattedContent,
                  source: event.log || event.progress_description || 'Unknown',
                  progress: event.progress,
                  log: event.log || '',
                  ...(event.progress === 'LAYOUT_ANALYZED' && {
                    layoutKey: layoutKey,
                    imageUrl: imageUrl
                  })
                });

                // Debug fallback layout artifact creation
                if (event.progress === 'LAYOUT_ANALYZED') {
                  console.log('🎨 Created fallback layout artifact:', {
                    name: artifactName,
                    progress: event.progress,
                    status: event.status,
                    layoutKey: layoutKey,
                    imageUrl: imageUrl,
                    rawMetadata: event.metadata,
                    rawContent: meta.data.data
                  });
                }
              }
              else if (meta.data.type === 'json') {
                const designTokens = this.formatDesignTokens(meta.data.data);

                artifacts.push({
                  name: 'Design System',
                  type: 'component',
                  content: 'Design system tokens and components',
                  tokens: designTokens,
                  source: event.log || event.progress_description || 'Unknown',
                  progress: event.progress,
                  log: event.log || ''
                });
              }
            }
            // Handle direct JSON artifacts (not nested in artifact wrapper)
            else if (meta.type === 'json') {
              console.log('🔄 Processing direct JSON artifact:', meta.data);
              const designTokens = this.formatDesignTokens(meta.data);

              artifacts.push({
                name: 'Design System',
                type: 'component',
                content: 'Design system tokens and components',
                tokens: designTokens,
                source: event.log || event.progress_description || 'Unknown',
                progress: event.progress,
                log: event.log || ''
              });
            }
            // NOTE: Removed files, repository, ref_code types as they are not proper artifacts
            // These should be handled separately (files in code tab, repository in settings, etc.)
          });
        }
      });

      console.log(`✅ Extracted ${artifacts.length} clean artifacts from metadata`);
      return artifacts;
    } catch (error) {
      console.error('Failed to extract artifacts from metadata:', error);
      return [];
    }
  }

  /**
   * Extract log entries from metadata SSE events
   * @param metadataString JSON string containing SSE events
   * @returns Array of log entries with chronological order
   */
  static extractLogEntriesFromMetadata(metadataString: string): Array<{
    log: string;
    progress: string;
    status: string;
    timestamp: number; // For chronological ordering
    progress_description: string;
  }> {
    try {
      const sseEvents = this.parseSSEEventsFromMetadata(metadataString);

      return sseEvents.map((event, index) => ({
        log: event.log || '',
        progress: event.progress,
        status: event.status,
        timestamp: index, // Use index for chronological order
        progress_description: event.progress_description || ''
      }));
    } catch (error) {
      console.error('Failed to extract log entries from metadata:', error);
      return [];
    }
  }

  /**
   * Generate artifact name based on progress and type
   * @param progress Progress stage
   * @param type Artifact type
   * @returns Generated artifact name
   */
  static generateArtifactName(progress: string, type: string): string {
    const progressNames: { [key: string]: string } = {
      'OVERVIEW': 'Project Overview',
      'SEED_PROJECT_INITIALIZED': 'Repository Setup',
      'DESIGN_SYSTEM_MAPPED': 'Design System',
      'COMPONENTS_CREATED': 'Components Generated',
      'LAYOUT_ANALYZED': 'Layout Analysis',
      'PAGES_GENERATED': 'Pages Generated',
      'BUILD': 'Build Artifacts',
      'DEPLOY': 'Deployment Info'
    };

    const typeSuffix = type === 'ref_code' ? ' URL' :
                      type === 'json' ? ' Data' :
                      type === 'files' ? ' Files' :
                      type === 'repository' ? ' Repository' :
                      type === 'text' ? '' : '';
    return `${progressNames[progress] || progress}${typeSuffix}`;
  }

  /**
   * Format text artifact content for proper display
   * @param content Raw text content
   * @param progress Progress stage
   * @returns Formatted content
   */
  static formatTextArtifactContent(content: string, progress: string): string {
    if (progress === 'OVERVIEW') {
      // Format Project Overview as markdown
      return `# Project Overview\n\n${content}`;
    } else if (progress === 'LAYOUT_ANALYZED') {
      // Format Layout Analysis as markdown
      return `# Layout Analysis\n\n${content}`;
    }
    return content;
  }

  /**
   * Extract layout key from metadata structure for LAYOUT_ANALYZED progress
   * @param metadataString Raw metadata string from API response
   * @param progress Current progress state
   * @param status Current status
   * @returns Layout key (matching layout animation component keys)
   */
  static extractLayoutKeyFromMetadata(metadataString: string, progress: string, status: string): string {
    console.log('🎨 Extracting layout key from metadata:', { progress, status });

    if (progress !== 'LAYOUT_ANALYZED' || status !== 'COMPLETED') {
      console.log('⚠️ Not a completed layout analysis, using fallback');
      return this.extractLayoutKey(''); // Fallback to old method
    }

    try {
      const metadata = JSON.parse(metadataString);

      if (Array.isArray(metadata)) {
        for (const metaItem of metadata) {
          if (metaItem.type === 'artifact' && metaItem.data) {
            if (metaItem.data.type === 'text' && metaItem.data.data) {
              const layoutKey = metaItem.data.data.trim().toUpperCase();

              // Validate that it's a proper layout key
              const validLayoutKeys = ['HB', 'HBF', 'HLSB', 'HLSBF', 'HBRS', 'HBRSF', 'HLSBRS', 'HLSBRSF'];
              if (validLayoutKeys.includes(layoutKey)) {
                console.log('✅ Found valid layout key from metadata:', layoutKey);
                return layoutKey;
              } else {
                console.warn('⚠️ Invalid layout key found:', layoutKey);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ Error parsing metadata for layout key:', error);
    }

    console.log('⚠️ No valid layout key found in metadata, using fallback');
    return 'HB'; // Default fallback
  }

  /**
   * Extract layout key from layout analysis content (fallback method)
   * @param content Layout analysis content
   * @returns Layout key (matching layout animation component keys)
   */
  static extractLayoutKey(content: string): string {
    console.log('🎨 Extracting layout key from content (fallback):', content);

    if (!content || typeof content !== 'string') {
      console.warn('⚠️ Invalid layout content:', content);
      return 'HB'; // Default to Header & Body
    }

    // If content is very short (like "HBF"), it might be a layout code already
    if (content.length <= 10) {
      console.log('⚠️ Short content detected, might be layout code:', content);
      const upperContent = content.toUpperCase();

      // Check if it's already a valid layout key
      const validLayoutKeys = ['HB', 'HBF', 'HLSB', 'HLSBF', 'HBRS', 'HBRSF', 'HLSBRS', 'HLSBRSF'];
      if (validLayoutKeys.includes(upperContent)) {
        console.log('✅ Found valid layout key:', upperContent);
        return upperContent;
      }
    }

    // Try to extract layout key from content descriptions
    const lowerContent = content.toLowerCase();

    // Map content descriptions to actual layout animation keys
    if (lowerContent.includes('sidebar') && lowerContent.includes('footer')) {
      console.log('✅ Detected sidebar with footer layout');
      return 'HLSBF'; // Header, Left Sidebar & Body & Footer
    } else if (lowerContent.includes('sidebar')) {
      console.log('✅ Detected sidebar layout');
      return 'HLSB'; // Header, Left Sidebar & Body
    } else if (lowerContent.includes('header') && lowerContent.includes('footer')) {
      console.log('✅ Detected header-footer layout');
      return 'HBF'; // Header, Body & Footer
    } else if (lowerContent.includes('dual sidebar') || (lowerContent.includes('left') && lowerContent.includes('right') && lowerContent.includes('sidebar'))) {
      console.log('✅ Detected dual sidebar layout');
      return 'HLSBRS'; // Header, Left Sidebar, Body & Right Sidebar
    } else if (lowerContent.includes('right sidebar')) {
      console.log('✅ Detected right sidebar layout');
      return 'HBRS'; // Header, Body & Right Sidebar
    } else if (lowerContent.includes('mobile-first') || lowerContent.includes('responsive')) {
      console.log('✅ Detected responsive layout (defaulting to HBF)');
      return 'HBF'; // Header, Body & Footer for responsive
    }

    // Fallback to default Header & Body
    console.log('🎨 Using default layout key (HB) for content:', content.substring(0, 100));
    return 'HB';
  }

  /**
   * Get layout image URL based on content
   * @param content Layout analysis content
   * @returns Image URL
   */
  static getLayoutImageUrl(content: string): string {
    const layoutKey = this.extractLayoutKey(content);
    return `assets/images/layout-${layoutKey}.png`;
  }

  /**
   * Format design tokens for artifacts display
   * @param tokenData Raw token data
   * @returns Formatted design tokens
   */
  static formatDesignTokens(tokenData: any): any {
    if (!tokenData) return { colors: [], fonts: [], buttons: [] };

    console.log('🎨 Formatting design tokens:', tokenData);

    // Handle the actual API structure: { design_tokens: { colors: [...] } }
    if (tokenData.design_tokens && tokenData.design_tokens.colors) {
      console.log('✅ Found design_tokens structure with colors:', tokenData.design_tokens.colors.length);

      return {
        colors: Array.isArray(tokenData.design_tokens.colors) ? tokenData.design_tokens.colors : [],
        fonts: Array.isArray(tokenData.design_tokens.fonts) ? tokenData.design_tokens.fonts : [],
        buttons: Array.isArray(tokenData.design_tokens.buttons) ? tokenData.design_tokens.buttons : []
      };
    }
    // Handle direct colors array format
    else if (tokenData.colors && Array.isArray(tokenData.colors)) {
      console.log('✅ Found direct colors array:', tokenData.colors.length);

      return {
        colors: tokenData.colors,
        fonts: Array.isArray(tokenData.fonts) ? tokenData.fonts : [],
        buttons: Array.isArray(tokenData.buttons) ? tokenData.buttons : []
      };
    }
    // Handle nested colors object format (mock data)
    else if (tokenData.colors && typeof tokenData.colors === 'object') {
      console.log('✅ Found colors object, converting to array');

      const colors = [];
      const colorObj = tokenData.colors;

      if (colorObj.primary) {
        colors.push({ id: 'primary', name: 'Primary', value: colorObj.primary });
      }
      if (colorObj.secondary) {
        colors.push({ id: 'secondary', name: 'Secondary', value: colorObj.secondary });
      }
      if (colorObj.accent) {
        colors.push({ id: 'accent', name: 'Accent', value: colorObj.accent });
      }

      // Handle typography if present
      const fonts = [];
      if (tokenData.typography) {
        if (tokenData.typography.fontFamily) {
          fonts.push({ id: 'body', name: 'Body Font', value: tokenData.typography.fontFamily });
        }
        if (tokenData.typography.headings) {
          fonts.push({ id: 'heading', name: 'Heading Font', value: tokenData.typography.headings });
        }
      }

      const result = {
        colors: colors,
        fonts: fonts,
        buttons: []
      };

      console.log('✅ Formatted design tokens result:', result);
      return result;
    }
    // Handle direct color properties (fallback)
    else if (tokenData.primary || tokenData.secondary) {
      console.log('✅ Found direct color properties');

      const colors = [];
      if (tokenData.primary) {
        colors.push({ id: 'primary', name: 'Primary', value: tokenData.primary });
      }
      if (tokenData.secondary) {
        colors.push({ id: 'secondary', name: 'Secondary', value: tokenData.secondary });
      }
      if (tokenData.accent) {
        colors.push({ id: 'accent', name: 'Accent', value: tokenData.accent });
      }

      return {
        colors: colors,
        fonts: [],
        buttons: []
      };
    }

    // Fallback for unknown format
    console.warn('⚠️ Unknown design token format:', tokenData);
    return { colors: [], fonts: [], buttons: [] };
  }

  /**
   * Check if conversation has regeneration data
   * @param conversation Array of conversation messages
   * @returns Object with regeneration status and file data
   */
  static analyzeRegenerationData(conversation: ConversationMessage[]): {
    hasRegenerations: boolean;
    regenerationFiles: FileData[];
    regenerationCount: number;
  } {
    if (!conversation || conversation.length === 0) {
      return {
        hasRegenerations: false,
        regenerationFiles: [],
        regenerationCount: 0
      };
    }

    // Count non-capsule messages (actual regenerations)
    const regenerationMessages = conversation.filter(msg => msg.message_type !== 'capsule');

    // Extract files from ui_metadata in conversation messages
    const regenerationFiles: FileData[] = [];

    regenerationMessages.forEach(msg => {
      if (msg.ui_metadata) {
        const files = this.parseUIMetadata(msg.ui_metadata);
        regenerationFiles.push(...files);
      }
    });

    return {
      hasRegenerations: regenerationMessages.length > 0,
      regenerationFiles,
      regenerationCount: regenerationMessages.length
    };
  }

  /**
   * Parse the main project loading response into structured data
   * @param response Raw project loading response from API
   * @returns Parsed project data
   */
  static parseProjectLoadingResponse(response: ProjectLoadingResponse): ParsedProjectData {
    try {
      const parsedData: ParsedProjectData = {
        // Project Details Section
        projectId: response.project_details.project_id,
        projectName: response.project_details.project_name,
        projectDescription: response.project_details.project_description,
        userPrompt: response.project_details.project_description, // Map to chat-window initial prompt
        
        // Project Settings Section
        device: response.project_settings.device,
        framework: response.project_settings.framework,
        designSystem: response.project_settings.design_system,
        generationType: response.project_settings.generation_type,
        
        // Repository Details Section
        cloneUrl: response.repository_details.clone_url,
        deployedUrl: this.cleanDeployedUrl(response.repository_details.deployed_url),

        // SSE Events Data (for first generation)
        metadata: response.metadata || '[]',

        // Code Files Data
        initialGenerationFiles: this.extractCodeFilesFromMetadata(response.metadata || '[]'),
        regenerationFiles: [], // Will be populated below

        // UI Design Data (for wireframe_generation projects)
        uiDesignPages: this.extractUIDesignPagesFromConversation(response.conversation, response.project_settings.generation_type),

        // Artifacts Data (from metadata SSE events)
        artifactsData: this.extractArtifactsFromMetadata(response.metadata || '[]'),

        // Log Entries Data (chronological SSE logs)
        logEntries: this.extractLogEntriesFromMetadata(response.metadata || '[]'),

        // Conversation Data
        conversationMessages: this.parseConversationMessages(response.conversation),
        commitIds: this.extractCommitIds(response.conversation),

        // Regeneration Analysis
        hasRegenerationData: false, // Will be updated below
        regenerationCount: 0, // Will be updated below
        firstCutMetadata: response.metadata ? this.parseFirstCutMetadata(response.metadata) : undefined
      };

      // Analyze regeneration data from conversation
      const regenerationAnalysis = this.analyzeRegenerationData(response.conversation);
      parsedData.hasRegenerationData = regenerationAnalysis.hasRegenerations;
      parsedData.regenerationCount = regenerationAnalysis.regenerationCount;
      parsedData.regenerationFiles = regenerationAnalysis.regenerationFiles;

      return parsedData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to parse project loading response: ${errorMessage}`);
    }
  }

  /**
   * Parse conversation messages into structured format
   * @param conversation Raw conversation messages
   * @returns Parsed conversation messages
   */
  static parseConversationMessages(conversation: ConversationMessage[]): ParsedConversationMessage[] {
    if (!conversation || conversation.length === 0) {
      return [];
    }

    return conversation.map(message => {
      const parsed: ParsedConversationMessage = {
        messageId: message.message_id,
        conversationId: message.conversation_id,
        messageType: message.message_type,
        content: message.content,
        createdAt: new Date(message.created_at)
      };

      // Parse UI metadata for assistant messages
      if (message.message_type === 'assistant' && message.ui_metadata) {
        parsed.parsedUIMetadata = this.parseUIMetadata(message.ui_metadata);
        parsed.fileData = this.extractFileDataFromUIMetadata(parsed.parsedUIMetadata);
      }

      // Extract commit ID for capsule messages
      if (message.message_type === 'capsule') {
        parsed.commitId = message.content;
      }

      return parsed;
    });
  }

  /**
   * Convert parsed conversation messages to chat window format
   * @param conversationMessages Parsed conversation messages
   * @returns Chat window messages
   */
  static convertToChatWindowMessages(conversationMessages: ParsedConversationMessage[]): ChatWindowMessage[] {
    return conversationMessages
      .filter(message => message.messageType !== 'capsule') // Exclude capsule messages from chat
      .map(message => {
        const chatMessage: ChatWindowMessage = {
          id: message.messageId,
          type: message.messageType === 'user' ? 'user' : 'assistant',
          content: message.content,
          timestamp: message.createdAt,
          metadata: message.parsedUIMetadata
        };

        // Add code files for assistant messages
        if (message.messageType === 'assistant' && message.fileData) {
          chatMessage.codeFiles = this.convertToCodeFiles(message.fileData);
        }

        return chatMessage;
      });
  }

  /**
   * Create stepper initialization data from parsed project data
   * @param parsedData Parsed project data
   * @returns Stepper initialization data
   */
  static createStepperInitializationData(parsedData: ParsedProjectData): StepperInitializationData {
    const steps: StepperStep[] = [];
    
    // If we have first-cut metadata, use it for initial steps
    if (parsedData.firstCutMetadata) {
      steps.push(...this.createStepsFromMetadata(parsedData.firstCutMetadata));
    } else {
      // Create default steps for project loading
      steps.push(
        {
          id: 'project-loaded',
          title: 'Project Loaded',
          description: `Loaded project: ${parsedData.projectName}`,
          status: 'completed',
          timestamp: new Date()
        }
      );
    }

    // Add regeneration steps if conversation data exists
    if (parsedData.hasRegenerationData) {
      const regenerationSteps = this.createRegenerationSteps(parsedData.conversationMessages);
      steps.push(...regenerationSteps);
    }

    return {
      projectId: parsedData.projectId,
      initialSteps: steps,
      currentProgress: {
        currentStepIndex: steps.length - 1,
        totalSteps: steps.length,
        progressPercentage: 100,
        description: 'Project loaded successfully'
      },
      status: 'COMPLETED'
    };
  }

  /**
   * Create version accordion data from commit IDs
   * @param commitIds Array of commit IDs
   * @param conversationMessages Conversation messages for context
   * @returns Version accordion data array
   */
  static createVersionAccordionData(
    commitIds: string[], 
    conversationMessages: ParsedConversationMessage[]
  ): VersionAccordionData[] {
    return commitIds.map((commitId, index) => {
      // Find the corresponding conversation message for context
      const relatedMessage = conversationMessages.find(msg => 
        msg.messageType === 'capsule' && msg.commitId === commitId
      );

      return {
        commitId,
        version: `v1.${index + 1}`,
        timestamp: relatedMessage ? relatedMessage.createdAt : new Date(),
        description: `Code regeneration ${index + 1}`,
        files: [] // Will be populated when template is loaded
      };
    });
  }

  /**
   * Create complete code window initialization data
   * @param parsedData Parsed project data
   * @returns Code window initialization data
   */
  static createCodeWindowInitializationData(parsedData: ParsedProjectData): CodeWindowInitializationData {
    const chatMessages = this.convertToChatWindowMessages(parsedData.conversationMessages);
    const stepperData = this.createStepperInitializationData(parsedData);
    const versionData = this.createVersionAccordionData(parsedData.commitIds, parsedData.conversationMessages);

    const repositoryData: RepositoryIntegrationData = {
      cloneUrl: parsedData.cloneUrl,
      deployedUrl: parsedData.deployedUrl,
      vcsProvider: 'azure_devops', // From the JSON example
      deploymentProvider: 'azure_static_web_apps', // From the JSON example
      projectId: parsedData.projectId
    };

    // For UI Design projects, extract comprehensive conversation data
    let uiDesignConversationData = null;
    if (parsedData.generationType === 'wireframe_generation' && parsedData.conversationMessages?.length) {
      // Convert ParsedConversationMessage[] back to ConversationMessage[] format for processing
      const conversationMessages = parsedData.conversationMessages.map(msg => ({
        message_id: msg.messageId,
        conversation_id: msg.conversationId,
        message_type: msg.messageType,
        content: msg.content,
        ui_metadata: msg.parsedUIMetadata ? JSON.stringify(msg.parsedUIMetadata) : null,
        created_at: msg.createdAt.toISOString()
      }));

      uiDesignConversationData = this.extractUIDesignConversationData(
        conversationMessages,
        parsedData.generationType
      );
    }

    return {
      mode: 'project-loading',
      projectData: parsedData,
      chatMessages,
      stepperData,
      versionData,
      repositoryData,
      uiDesignConversationData
    };
  }

  // Private helper methods

  private static cleanDeployedUrl(deployedUrl: string): string {
    if (!deployedUrl) return '';
    // Remove ANSI escape codes like [0m
    return deployedUrl.replace(/\[[0-9;]*m/g, '').trim();
  }

  private static extractCommitIds(conversation: ConversationMessage[]): string[] {
    return conversation
      .filter(message => message.message_type === 'capsule')
      .map(message => message.content)
      .filter(content => content && content.length > 0);
  }

  private static parseUIMetadata(uiMetadata: string): any {
    try {
      return JSON.parse(uiMetadata);
    } catch (error) {
      console.warn('Failed to parse UI metadata:', error);
      return null;
    }
  }

  private static extractFileDataFromUIMetadata(metadata: any): FileData[] {
    if (!metadata || !Array.isArray(metadata)) return [];

    const files: FileData[] = [];

    metadata.forEach((item: any) => {
      if (item.data && Array.isArray(item.data)) {
        item.data.forEach((file: any) => {
          if (file.fileName && file.content) {
            files.push({
              fileName: file.fileName,
              content: file.content,
              path: file.fileName,
              language: this.getLanguageFromFileName(file.fileName)
            });
          }
        });
      }
    });

    return files;
  }

  /**
   * Normalize file name for intelligent deduplication
   * Handles cases like "Login_Page.html" vs "Login Page" by creating consistent keys
   * @param fileName Original file name
   * @returns Normalized file name for deduplication
   */
  private static normalizeFileName(fileName: string): string {
    if (!fileName || typeof fileName !== 'string') {
      return 'untitled';
    }

    return fileName
      .replace(/\.html?$/i, '') // Remove .html or .htm extension
      .replace(/[_\-\s]+/g, '_') // Replace spaces, hyphens, underscores with single underscore
      .toLowerCase() // Convert to lowercase for case-insensitive comparison
      .trim();
  }

  /**
   * Extract UI Design pages from conversation ui_metadata for wireframe_generation projects
   * @param metadata Parsed UI metadata from conversation messages
   * @returns Array of UI Design pages
   */
  private static extractUIDesignPagesFromUIMetadata(metadata: any): UIDesignPage[] {
    if (!metadata || !Array.isArray(metadata)) return [];

    const pages: UIDesignPage[] = [];

    // For wireframe_generation projects, ui_metadata is directly an array of files
    metadata.forEach((file: any) => {
      if (file.fileName && file.content) {
        pages.push({
          fileName: file.fileName,
          content: file.content
        });
      }
    });

    return pages;
  }

  /**
   * Parse conversation into generation phases for UI Design projects
   * @param conversation Array of conversation messages
   * @returns Parsed conversation data with generation phases
   */
  private static parseUIDesignConversationPhases(conversation: ConversationMessage[]): {
    firstGeneration: ConversationMessage[];
    regenerations: ConversationMessage[][];
    allFiles: Map<string, { fileName: string; content: string; commitId: string; generationIndex: number }>;
    conversationHistory: { userPrompt: string; assistantResponse: string; commitId?: string }[];
  } {
    const firstGeneration: ConversationMessage[] = [];
    const regenerations: ConversationMessage[][] = [];
    const allFiles = new Map<string, { fileName: string; content: string; commitId: string; generationIndex: number }>();
    const conversationHistory: { userPrompt: string; assistantResponse: string; commitId?: string }[] = [];

    // Group messages into sets of 3 (user → assistant → capsule)
    for (let i = 0; i < conversation.length; i += 3) {
      const userMessage = conversation[i];
      const assistantMessage = conversation[i + 1];
      const capsuleMessage = conversation[i + 2];

      if (!userMessage || !assistantMessage || !capsuleMessage) break;

      const messageGroup = [userMessage, assistantMessage, capsuleMessage];

      // Add to conversation history
      conversationHistory.push({
        userPrompt: userMessage.content || '',
        assistantResponse: assistantMessage.content || '',
        commitId: capsuleMessage.content || undefined
      });

      if (i === 0) {
        // First generation
        firstGeneration.push(...messageGroup);
      } else {
        // Regeneration
        regenerations.push(messageGroup);
      }

      // Extract files from capsule ui_metadata with comprehensive error handling
      if (capsuleMessage.ui_metadata) {
        try {
          let metadata;

          // Handle double-encoded JSON strings with robust parsing
          if (typeof capsuleMessage.ui_metadata === 'string') {
            try {
              // First parse to handle outer quotes
              const firstParse = JSON.parse(capsuleMessage.ui_metadata);
              // If it's still a string, parse again
              metadata = typeof firstParse === 'string' ? JSON.parse(firstParse) : firstParse;
            } catch (parseError) {
              console.warn('Failed to parse ui_metadata JSON string:', {
                error: parseError,
                rawData: capsuleMessage.ui_metadata?.substring(0, 200) + '...',
                messageId: capsuleMessage.message_id
              });
              continue; // Skip this message and continue with next
            }
          } else {
            metadata = capsuleMessage.ui_metadata;
          }

          // Validate metadata structure
          if (!Array.isArray(metadata)) {
            console.warn('ui_metadata is not an array:', {
              type: typeof metadata,
              messageId: capsuleMessage.message_id,
              metadata: metadata
            });
            continue; // Skip this message
          }

          const generationIndex = Math.floor(i / 3);
          let filesProcessedInThisMessage = 0;

          metadata.forEach((file: any, fileIndex: number) => {
            try {
              // Validate file structure
              if (!file || typeof file !== 'object') {
                console.warn('Invalid file object in ui_metadata:', {
                  fileIndex,
                  messageId: capsuleMessage.message_id,
                  file: file
                });
                return;
              }

              if (!file.fileName || !file.content) {
                console.warn('Missing fileName or content in ui_metadata file:', {
                  fileIndex,
                  messageId: capsuleMessage.message_id,
                  hasFileName: !!file.fileName,
                  hasContent: !!file.content,
                  fileName: file.fileName
                });
                return;
              }

              // Validate content is not empty
              if (typeof file.content !== 'string' || file.content.trim().length === 0) {
                console.warn('Empty or invalid content in ui_metadata file:', {
                  fileIndex,
                  messageId: capsuleMessage.message_id,
                  fileName: file.fileName,
                  contentType: typeof file.content,
                  contentLength: file.content?.length || 0
                });
                return;
              }

              // ENHANCED: Intelligent file deduplication based on normalized file names
              // Handle cases like "Login_Page.html" vs "Login Page" by normalizing both
              const normalizedFileName = this.normalizeFileName(file.fileName);
              const isUpdate = allFiles.has(normalizedFileName);

              // Track file versions - update existing or create new (latest version wins)
              allFiles.set(normalizedFileName, {
                fileName: file.fileName, // Keep original fileName for display
                content: file.content,
                commitId: capsuleMessage.content || '',
                generationIndex
              });

              console.log(`${isUpdate ? '🔄 Updated' : '✅ Added'} UI Design file:`, {
                originalFileName: file.fileName,
                normalizedKey: normalizedFileName,
                generationIndex,
                isUpdate
              });

              filesProcessedInThisMessage++;
            } catch (fileError) {
              console.error('Error processing individual file in ui_metadata:', {
                error: fileError,
                fileIndex,
                messageId: capsuleMessage.message_id,
                fileName: file?.fileName
              });
            }
          });

          console.log('✅ Processed ui_metadata files:', {
            messageId: capsuleMessage.message_id,
            generationIndex,
            filesInMetadata: metadata.length,
            filesProcessed: filesProcessedInThisMessage,
            commitId: capsuleMessage.content
          });

        } catch (error) {
          console.error('Critical error parsing ui_metadata in conversation phase parsing:', {
            error: error,
            messageId: capsuleMessage.message_id,
            messageType: capsuleMessage.message_type,
            hasUIMetadata: !!capsuleMessage.ui_metadata,
            uiMetadataType: typeof capsuleMessage.ui_metadata,
            uiMetadataPreview: typeof capsuleMessage.ui_metadata === 'string'
              ? capsuleMessage.ui_metadata.substring(0, 100) + '...'
              : 'Not a string'
          });
        }
      }
    }

    console.log('🔍 Parsed UI Design conversation phases:', {
      firstGenerationMessages: firstGeneration.length,
      regenerationCount: regenerations.length,
      totalFiles: allFiles.size,
      conversationHistoryLength: conversationHistory.length,
      fileNames: Array.from(allFiles.keys())
    });

    return { firstGeneration, regenerations, allFiles, conversationHistory };
  }

  /**
   * Extract UI Design pages from conversation messages for wireframe_generation projects
   * Uses comprehensive conversation parsing with version control
   * @param conversation Array of conversation messages
   * @param generationType The generation type to determine extraction method
   * @returns Array of UI Design pages (latest versions only)
   */
  private static extractUIDesignPagesFromConversation(
    conversation: ConversationMessage[],
    generationType: string
  ): Array<{ fileName: string; content: string }> {
    if (generationType !== 'wireframe_generation' || !conversation || conversation.length === 0) {
      return [];
    }

    // Use comprehensive conversation parsing
    const { allFiles } = this.parseUIDesignConversationPhases(conversation);

    // Convert to array format (latest versions only)
    const pages: Array<{ fileName: string; content: string }> = [];
    allFiles.forEach(fileData => {
      pages.push({
        fileName: fileData.fileName,
        content: fileData.content
      });
    });

    console.log('✅ Extracted UI Design pages from conversation with version control:', {
      generationType,
      pagesCount: pages.length,
      fileNames: pages.map(p => p.fileName),
      totalVersions: allFiles.size
    });

    return pages;
  }

  /**
   * Extract comprehensive UI Design conversation data for project loading
   * @param conversation Array of conversation messages
   * @param generationType Project generation type
   * @returns Comprehensive conversation data including chat history and file versions
   */
  static extractUIDesignConversationData(
    conversation: ConversationMessage[],
    generationType: string
  ): {
    chatMessages: Array<{ userPrompt: string; assistantResponse: string; commitId?: string }>;
    fileVersions: Map<string, { fileName: string; content: string; commitId: string; generationIndex: number }>;
    latestFiles: Array<{ fileName: string; content: string }>;
    generationCount: number;
  } {
    if (generationType !== 'wireframe_generation' || !conversation?.length) {
      return {
        chatMessages: [],
        fileVersions: new Map(),
        latestFiles: [],
        generationCount: 0
      };
    }

    const { conversationHistory, allFiles } = this.parseUIDesignConversationPhases(conversation);

    // Convert file map to latest files array
    const latestFiles: Array<{ fileName: string; content: string }> = [];
    allFiles.forEach(fileData => {
      latestFiles.push({
        fileName: fileData.fileName,
        content: fileData.content
      });
    });

    const result = {
      chatMessages: conversationHistory,
      fileVersions: allFiles,
      latestFiles,
      generationCount: conversationHistory.length
    };

    console.log('🎨 Extracted comprehensive UI Design conversation data:', {
      generationType,
      chatMessagesCount: result.chatMessages.length,
      fileVersionsCount: result.fileVersions.size,
      latestFilesCount: result.latestFiles.length,
      generationCount: result.generationCount
    });

    return result;
  }

  private static convertToCodeFiles(fileData: FileData[]): ChatCodeFile[] {
    return fileData.map(file => ({
      name: file.fileName,
      content: file.content,
      language: file.language || 'plaintext',
      path: file.path
    }));
  }

  private static getLanguageFromFileName(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    const languageMap: { [key: string]: string } = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'java': 'java',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml'
    };
    
    return languageMap[extension || ''] || 'plaintext';
  }

  private static parseFirstCutMetadata(_metadata: any): FirstCutMetadata | undefined {
    // This would parse the first-cut generation metadata
    // Implementation depends on the actual metadata structure
    return undefined;
  }

  private static createStepsFromMetadata(_metadata: FirstCutMetadata): StepperStep[] {
    // Create steps from first-cut metadata
    return [];
  }

  private static createRegenerationSteps(conversationMessages: ParsedConversationMessage[]): StepperStep[] {
    const steps: StepperStep[] = [];
    
    conversationMessages.forEach((message, index) => {
      if (message.messageType === 'user') {
        steps.push({
          id: `regeneration-${index}`,
          title: 'Code Regeneration',
          description: message.content,
          status: 'completed',
          timestamp: message.createdAt
        });
      }
    });
    
    return steps;
  }
}
