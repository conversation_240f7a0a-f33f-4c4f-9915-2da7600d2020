import { Injectable, signal, computed, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { 
  CardOption, 
  EnhancedCardOption, 
  FilterState, 
  SortOption, 
  PROJECT_TYPES, 
  ProjectType,
  SearchFilterConfig 
} from '../models/recent-creation.model';

/**
 * Service for managing project filtering, searching, and sorting functionality
 * Uses Angular 19+ patterns with signals and modern dependency injection
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectFilterService {
  private readonly destroyRef = inject(DestroyRef);
  
  // Search term subject for debouncing
  private readonly searchSubject = new Subject<string>();
  
  // Filter state signals
  private readonly _searchTerm = signal<string>('');
  private readonly _selectedProjectTypes = signal<string[]>([]);
  private readonly _sortBy = signal<SortOption>('modified-desc');
  private readonly _showFilters = signal<boolean>(false);
  
  // Configuration signal
  private readonly _config = signal<SearchFilterConfig>({
    enableSearch: true,
    enableProjectTypeFilter: true,
    enableSorting: true,
    defaultSort: 'modified-desc',
    searchPlaceholder: 'Search projects...',
    debounceTime: 300
  });
  
  // Public readonly signals
  readonly searchTerm = this._searchTerm.asReadonly();
  readonly selectedProjectTypes = this._selectedProjectTypes.asReadonly();
  readonly sortBy = this._sortBy.asReadonly();
  readonly showFilters = this._showFilters.asReadonly();
  readonly config = this._config.asReadonly();
  
  // Computed filter state
  readonly filterState = computed<FilterState>(() => ({
    searchTerm: this._searchTerm(),
    selectedProjectTypes: this._selectedProjectTypes(),
    sortBy: this._sortBy(),
    showFilters: this._showFilters()
  }));
  
  // Computed active filter count
  readonly activeFilterCount = computed(() => {
    let count = 0;
    if (this._searchTerm().trim()) count++;
    if (this._selectedProjectTypes().length > 0) count++;
    return count;
  });
  
  // Available project types for filtering
  readonly availableProjectTypes = computed(() => Object.values(PROJECT_TYPES));
  
  constructor() {
    this.setupSearchDebouncing();
  }
  
  /**
   * Setup search term debouncing to improve performance
   */
  private setupSearchDebouncing(): void {
    this.searchSubject.pipe(
      debounceTime(this._config().debounceTime),
      distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(searchTerm => {
      this._searchTerm.set(searchTerm);
    });
  }
  
  /**
   * Update search term with debouncing
   */
  updateSearchTerm(term: string): void {
    this.searchSubject.next(term);
  }
  
  /**
   * Set search term immediately (for programmatic updates)
   */
  setSearchTerm(term: string): void {
    this._searchTerm.set(term);
  }
  
  /**
   * Toggle project type filter
   */
  toggleProjectType(projectType: string): void {
    const current = this._selectedProjectTypes();
    const index = current.indexOf(projectType);
    
    if (index === -1) {
      this._selectedProjectTypes.set([...current, projectType]);
    } else {
      this._selectedProjectTypes.set(current.filter(type => type !== projectType));
    }
  }
  
  /**
   * Set selected project types
   */
  setSelectedProjectTypes(types: string[]): void {
    this._selectedProjectTypes.set(types);
  }
  
  /**
   * Update sort option
   */
  setSortBy(sortOption: SortOption): void {
    this._sortBy.set(sortOption);
  }
  
  /**
   * Toggle filter panel visibility
   */
  toggleFilters(): void {
    this._showFilters.set(!this._showFilters());
  }
  
  /**
   * Set filter panel visibility
   */
  setShowFilters(show: boolean): void {
    this._showFilters.set(show);
  }
  
  /**
   * Clear all filters and search
   */
  clearAllFilters(): void {
    this._searchTerm.set('');
    this._selectedProjectTypes.set([]);
    this._sortBy.set(this._config().defaultSort);
    this.searchSubject.next('');
  }
  
  /**
   * Reset to default state
   */
  reset(): void {
    this.clearAllFilters();
    this._showFilters.set(false);
  }
  
  /**
   * Update service configuration
   */
  updateConfig(config: Partial<SearchFilterConfig>): void {
    this._config.set({ ...this._config(), ...config });
  }
  
  /**
   * Get project type definition by value
   */
  getProjectType(value: string): ProjectType | undefined {
    return PROJECT_TYPES[value];
  }
  
  /**
   * Convert CardOption to EnhancedCardOption with project type metadata
   */
  enhanceCardOption(cardOption: CardOption): EnhancedCardOption {
    const projectType = this.getProjectType(cardOption.type) || {
      value: cardOption.type,
      label: cardOption.type.charAt(0).toUpperCase() + cardOption.type.slice(1),
      color: '#6c757d',
      description: 'Unknown project type'
    };
    
    return {
      ...cardOption,
      projectType,
      lastModified: cardOption.timestamp
    };
  }
  
  /**
   * Filter and sort projects based on current filter state
   */
  filterAndSortProjects(projects: CardOption[]): EnhancedCardOption[] {
    try {
      if (!Array.isArray(projects)) {
        console.warn('ProjectFilterService: Invalid projects array provided');
        return [];
      }

      const enhanced = projects.map(project => this.enhanceCardOption(project));

      // Apply search filter
      let filtered = enhanced;
      const searchTerm = this._searchTerm().toLowerCase().trim();
      if (searchTerm) {
        filtered = enhanced.filter(project => {
          try {
            return project.heading?.toLowerCase().includes(searchTerm) ||
                   project.description?.toLowerCase().includes(searchTerm) ||
                   project.projectType?.label?.toLowerCase().includes(searchTerm);
          } catch (error) {
            console.warn('ProjectFilterService: Error filtering project', project, error);
            return false;
          }
        });
      }

      // Apply project type filter
      const selectedTypes = this._selectedProjectTypes();
      if (selectedTypes.length > 0) {
        filtered = filtered.filter(project => {
          try {
            return selectedTypes.includes(project.projectType?.value);
          } catch (error) {
            console.warn('ProjectFilterService: Error filtering by project type', project, error);
            return false;
          }
        });
      }

      // Apply sorting
      const sortBy = this._sortBy();
      filtered.sort((a, b) => {
        try {
          switch (sortBy) {
            case 'name-asc':
              return (a.heading || '').localeCompare(b.heading || '');
            case 'name-desc':
              return (b.heading || '').localeCompare(a.heading || '');
            case 'date-asc':
              return this.compareDates(a.createdDate, b.createdDate);
            case 'date-desc':
              return this.compareDates(b.createdDate, a.createdDate);
            case 'modified-asc':
              return this.compareDates(a.lastModified, b.lastModified);
            case 'modified-desc':
            default:
              return this.compareDates(b.lastModified, a.lastModified);
          }
        } catch (error) {
          console.warn('ProjectFilterService: Error sorting projects', error);
          return 0;
        }
      });

      return filtered;
    } catch (error) {
      console.error('ProjectFilterService: Error in filterAndSortProjects', error);
      return [];
    }
  }

  /**
   * Safely compare dates for sorting
   */
  private compareDates(dateA?: string, dateB?: string): number {
    const timeA = dateA ? new Date(dateA).getTime() : 0;
    const timeB = dateB ? new Date(dateB).getTime() : 0;

    // Handle invalid dates
    if (isNaN(timeA) && isNaN(timeB)) return 0;
    if (isNaN(timeA)) return 1;
    if (isNaN(timeB)) return -1;

    return timeA - timeB;
  }
}
