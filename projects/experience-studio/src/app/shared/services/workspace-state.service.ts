import { Injectable, signal, computed } from '@angular/core';

export type WorkspaceView = 'dashboard' | 'showcase';
export type Theme = 'light' | 'dark' | 'system';

export interface FilterState {
  searchQuery: string;
  projectTypes: string[];
  technologies: string[];
  difficulty: 'all' | 'beginner' | 'intermediate' | 'advanced';
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  creator: string;
  sortBy: 'relevance' | 'date' | 'rating' | 'views' | 'name';
  sortOrder: 'asc' | 'desc';
}

export interface CommunityProject {
  id: string;
  title: string;
  description: string;
  type: string;
  thumbnail?: string;
  creator: {
    id: string;
    name: string;
    avatar: string;
  };
  rating: number;
  views: number;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Workspace State Service - Centralized state management for MLO Workspace
 * Uses Angular 19+ signals for reactive state management
 * 
 * Features:
 * - Theme management with system preference detection
 * - Search and filter state management
 * - Community projects data management
 * - Global loading and error states
 * - User preferences persistence
 */
@Injectable({
  providedIn: 'root'
})
export class WorkspaceStateService {
  // Core workspace state signals
  private readonly _currentView = signal<WorkspaceView>('dashboard');
  private readonly _theme = signal<Theme>('system');
  private readonly _isGlobalLoading = signal<boolean>(false);
  private readonly _globalError = signal<string | null>(null);

  // Search and filter state
  private readonly _searchQuery = signal<string>('');
  private readonly _activeFilters = signal<FilterState>({
    searchQuery: '',
    projectTypes: [],
    technologies: [],
    difficulty: 'all',
    dateRange: { start: null, end: null },
    creator: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Community projects state
  private readonly _communityProjects = signal<CommunityProject[]>([]);
  private readonly _featuredProjects = signal<CommunityProject[]>([]);
  private readonly _totalResults = signal<number>(0);
  private readonly _currentPage = signal<number>(1);
  private readonly _isLoading = signal<boolean>(false);

  // User preferences
  private readonly _userPreferences = signal<{
    theme: Theme;
    defaultView: WorkspaceView;
    filtersExpanded: boolean;
  }>({
    theme: 'system',
    defaultView: 'dashboard',
    filtersExpanded: false
  });

  // Public readonly signals
  readonly currentView = this._currentView.asReadonly();
  readonly theme = this._theme.asReadonly();
  readonly isGlobalLoading = this._isGlobalLoading.asReadonly();
  readonly globalError = this._globalError.asReadonly();
  readonly searchQuery = this._searchQuery.asReadonly();
  readonly activeFilters = this._activeFilters.asReadonly();
  readonly communityProjects = this._communityProjects.asReadonly();
  readonly featuredProjects = this._featuredProjects.asReadonly();
  readonly totalResults = this._totalResults.asReadonly();
  readonly currentPage = this._currentPage.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly userPreferences = this._userPreferences.asReadonly();

  // Computed values
  readonly effectiveTheme = computed(() => {
    const theme = this._theme();
    if (theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return theme;
  });

  readonly filteredProjects = computed(() => {
    const projects = this._communityProjects();
    const filters = this._activeFilters();
    
    return this.applyFilters(projects, filters);
  });

  readonly hasActiveFilters = computed(() => {
    const filters = this._activeFilters();
    return filters.projectTypes.length > 0 ||
           filters.technologies.length > 0 ||
           filters.difficulty !== 'all' ||
           filters.dateRange.start !== null ||
           filters.dateRange.end !== null ||
           filters.creator !== '' ||
           filters.searchQuery !== '';
  });

  readonly activeFilterCount = computed(() => {
    const filters = this._activeFilters();
    let count = 0;
    
    if (filters.projectTypes.length > 0) count++;
    if (filters.technologies.length > 0) count++;
    if (filters.difficulty !== 'all') count++;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.creator) count++;
    if (filters.searchQuery) count++;
    
    return count;
  });

  constructor() {
    this.setupSystemThemeListener();
  }

  // Navigation methods
  setCurrentView(view: WorkspaceView): void {
    this._currentView.set(view);
  }

  // Theme methods
  setTheme(theme: Theme): void {
    this._theme.set(theme);
    this.saveUserPreferences();
    this.applyThemeToDocument();
  }

  // Loading and error methods
  setGlobalLoading(loading: boolean): void {
    this._isGlobalLoading.set(loading);
  }

  setGlobalError(error: string | null): void {
    this._globalError.set(error);
  }

  clearGlobalError(): void {
    this._globalError.set(null);
  }

  // Search and filter methods
  setSearchQuery(query: string): void {
    this._searchQuery.set(query);
    this.updateFilters({ searchQuery: query });

    // Trigger search in community projects service if we have a query
    if (query.trim()) {
      // This will be handled by the showcase page component
      // to avoid circular dependencies
    }
  }

  updateFilters(partialFilters: Partial<FilterState>): void {
    const currentFilters = this._activeFilters();
    this._activeFilters.set({ ...currentFilters, ...partialFilters });
  }

  clearAllFilters(): void {
    this._activeFilters.set({
      searchQuery: '',
      projectTypes: [],
      technologies: [],
      difficulty: 'all',
      dateRange: { start: null, end: null },
      creator: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    this._searchQuery.set('');
  }

  // Community projects methods
  setCommunityProjects(projects: CommunityProject[]): void {
    this._communityProjects.set(projects);
  }

  setFeaturedProjects(projects: CommunityProject[]): void {
    this._featuredProjects.set(projects);
  }

  setTotalResults(total: number): void {
    this._totalResults.set(total);
  }

  setCurrentPage(page: number): void {
    this._currentPage.set(page);
  }

  setLoading(loading: boolean): void {
    this._isLoading.set(loading);
  }

  // Initialization methods
  loadUserPreferences(): void {
    try {
      const saved = localStorage.getItem('mlo-workspace-preferences');
      if (saved) {
        const preferences = JSON.parse(saved);
        this._userPreferences.set(preferences);
        this._theme.set(preferences.theme);
        this._currentView.set(preferences.defaultView);
      }
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
    }
  }

  saveUserPreferences(): void {
    try {
      const preferences = {
        theme: this._theme(),
        defaultView: this._currentView(),
        filtersExpanded: this._userPreferences().filtersExpanded
      };
      this._userPreferences.set(preferences);
      localStorage.setItem('mlo-workspace-preferences', JSON.stringify(preferences));
    } catch (error) {
      console.warn('Failed to save user preferences:', error);
    }
  }

  initializeWorkspace(): void {
    this.applyThemeToDocument();
    // Additional initialization logic can be added here
  }

  // Private helper methods
  private setupSystemThemeListener(): void {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', () => {
      if (this._theme() === 'system') {
        this.applyThemeToDocument();
      }
    });
  }



  private applyThemeToDocument(): void {
    const theme = this.effectiveTheme();
    document.documentElement.setAttribute('data-theme', theme);
    document.body.className = theme === 'dark' ? 'dark-theme' : 'light-theme';
  }

  private applyFilters(projects: CommunityProject[], filters: FilterState): CommunityProject[] {
    return projects.filter(project => {
      // Search query filter
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        const matchesSearch = 
          project.title.toLowerCase().includes(query) ||
          project.description.toLowerCase().includes(query) ||
          project.tags.some(tag => tag.toLowerCase().includes(query)) ||
          project.creator.name.toLowerCase().includes(query);
        
        if (!matchesSearch) return false;
      }

      // Project type filter
      if (filters.projectTypes.length > 0 && !filters.projectTypes.includes(project.type)) {
        return false;
      }

      // Technology filter
      if (filters.technologies.length > 0) {
        const hasMatchingTech = filters.technologies.some(tech => 
          project.tags.some(tag => tag.toLowerCase().includes(tech.toLowerCase()))
        );
        if (!hasMatchingTech) return false;
      }

      // Difficulty filter
      if (filters.difficulty !== 'all' && project.difficulty !== filters.difficulty) {
        return false;
      }

      // Date range filter
      if (filters.dateRange.start && project.createdAt < filters.dateRange.start) {
        return false;
      }
      if (filters.dateRange.end && project.createdAt > filters.dateRange.end) {
        return false;
      }

      // Creator filter
      if (filters.creator && !project.creator.name.toLowerCase().includes(filters.creator.toLowerCase())) {
        return false;
      }

      return true;
    }).sort((a, b) => {
      // Apply sorting
      const { sortBy, sortOrder } = filters;
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          comparison = a.createdAt.getTime() - b.createdAt.getTime();
          break;
        case 'rating':
          comparison = a.rating - b.rating;
          break;
        case 'views':
          comparison = a.views - b.views;
          break;
        case 'name':
          comparison = a.title.localeCompare(b.title);
          break;
        default: // relevance
          comparison = b.rating - a.rating; // Default to rating for relevance
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }
}
