import { Component, OnInit, OnD<PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { ButtonComponent, CaptionComponent, HeadingComponent, IconsComponent, BodyTextComponent, InputComponent } from '@awe/play-comp-library';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { FeatureDataService, FeatureCard, FeatureSection } from '../../services/feature-data.service';

@Component({
  selector: 'app-feature-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    HeadingComponent,
    AweCardComponent,
    IconsComponent,
    AweModalComponent,
    BodyTextComponent,
    IconsComponent,
    InputComponent,
    
  ],
  templateUrl: './feature-list.component.html',
  styleUrls: ['./feature-list.component.scss'], // Corrected property name
})
export class FeatureListComponent implements OnInit, OnDestroy {
  // Subscription management
  private subscription = new Subscription();

  roboBallIcon: string = '/icons/robo_ball.svg';
  threeDotsIcon: string = 'icons/three-dot.svg';

  // Modal State
  isEditModalOpen = false;
  selectedFeatureForEdit: FeatureCard | null = null;
  isAddingNewFeature = false; // Track if we're adding new or editing existing
  currentSectionId: string = ''; // Track which section we're adding to
  // Create a working copy for editing to avoid direct mutation until save
  editableFeatureTitle: string = '';
  editableFeatureDescription: string = '';
  editableFeatureTags: string[] = [];
  regeneratePrompt: string = '';

  openDropdownId: string | null = null;

  sections: FeatureSection[] = [];

  constructor(
    private cdRef: ChangeDetectorRef,
    private featureDataService: FeatureDataService
  ) {}

  ngOnInit(): void {
    // Subscribe to data changes
    this.subscription.add(
      this.featureDataService.sections$.subscribe(
        (sections) => {
          this.sections = sections;
        }
      )
    );

    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-menu-container')) {
        this.closeAllDropdowns();
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  getSectionIds(): string[] {
    return this.featureDataService.getSectionIds();
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      // Reorder within same section
      const sectionId = event.container.id;
      this.featureDataService.reorderFeatures(sectionId, event.previousIndex, event.currentIndex);
    } else {
      // Move between sections
      const fromSectionId = event.previousContainer.id;
      const toSectionId = event.container.id;
      const featureId = event.previousContainer.data[event.previousIndex].id;

      this.featureDataService.moveFeature(featureId, fromSectionId, toSectionId, event.currentIndex);
    }
  }

  addNewFeature(sectionId: string): void {
    // Open modal for adding new feature
    this.isAddingNewFeature = true;
    this.currentSectionId = sectionId;
    this.selectedFeatureForEdit = null;
    // Clear editable data for new feature
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
  }

  deleteFeature(sectionId: string, featureId: string): void {
    this.featureDataService.deleteFeature(featureId);
    this.closeAllDropdowns();
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();
    this.openDropdownId = this.openDropdownId === featureId ? null : featureId;
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  openEditModal(feature: FeatureCard): void {
    this.isAddingNewFeature = false; // We're editing, not adding
    this.selectedFeatureForEdit = { ...feature }; // Edit a copy
    this.currentSectionId = ''; // Not needed for editing
    // Set up editable data
    this.editableFeatureTitle = feature.title;
    this.editableFeatureDescription = feature.description;
    this.editableFeatureTags = [...feature.tags]; // Copy array
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedFeatureForEdit = null;
    this.isAddingNewFeature = false;
    this.currentSectionId = '';
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [];
    this.regeneratePrompt = '';
  }

  updateFeature(): void {
    // Validate required fields
    if (!this.editableFeatureTitle.trim()) {
      alert('Please enter a feature title');
      return;
    }

    if (this.isAddingNewFeature) {
      // Adding new feature via service
      this.featureDataService.addFeature(this.currentSectionId, {
        title: this.editableFeatureTitle.trim(),
        description: this.editableFeatureDescription.trim() || 'No description provided',
        tags: this.editableFeatureTags
          .filter((tag) => tag.trim() !== '')
          .map((tag) => tag.trim()),
      });
    } else {
      // Updating existing feature via service
      if (!this.selectedFeatureForEdit) return;

      this.featureDataService.updateFeature(this.selectedFeatureForEdit.id, {
        title: this.editableFeatureTitle.trim(),
        description: this.editableFeatureDescription.trim() || 'No description provided',
        tags: this.editableFeatureTags
          .filter((tag) => tag.trim() !== '')
          .map((tag) => tag.trim()),
      });
    }

    this.closeEditModal();
  }

  // Methods for managing editable tags in the modal
  addEditableTag(): void {
    this.editableFeatureTags.push(''); // Add a new empty string to edit
    this.cdRef.detectChanges(); // Ensure ngFor updates
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-tag-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableTag(index: number): void {
    this.editableFeatureTags.splice(index, 1);
  }

  // Required for ngFor with [(ngModel)] on primitive types like string
  trackByFn(index: number, _item: any): any {
    return index;
  }

  // This method is for direct delete from card, called by (click) on delete button
  handleDeleteFeatureFromCard(sectionId: string, featureId: string) {
    this.deleteFeature(sectionId, featureId);
    this.closeAllDropdowns();
  }
}
