import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  GanttChartComponent,
  GanttTask,
} from '../../components/gantt-chart/gantt-chart.component';
import {
  DatepickerComponent,
  DropdownComponent,
  HeadingComponent,
  IconsComponent,
  InputComponent,
} from '@awe/play-comp-library';
import { ProductRoadmapCardViewComponent } from '../product-roadmap-card-view/product-roadmap-card-view.component';
import {
  RoadmapDataService,
  RoadmapTask,
  Priority,
} from '../../services/roadmap-data.service';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';

@Component({
  selector: 'app-product-roadmap',
  standalone: true,
  imports: [
    GanttChartComponent,
    CommonModule,
    FormsModule,
    IconsComponent,
    DropdownComponent,
    DatepickerComponent,
    ProductRoadmapCardViewComponent,
    AweModalComponent,
    HeadingComponent,
    InputComponent,
  ],
  templateUrl: './product-roadmap.component.html',
  styleUrl: './product-roadmap.component.scss',
})
export class ProductRoadmapComponent implements OnInit, OnDestroy {
  chartView: string = 'assets/icons/chart-view.png';
  cardView: string = 'assets/icons/card-view.png';
  activeView: 'timeline' | 'card' = 'timeline'; // Changed to represent view types

  projectTasks: GanttTask[] = [];
  private subscription = new Subscription();

  // Modal State
  isEditModalOpen = false;
  selectedTaskForEdit: RoadmapTask | null = null;
  isAddingNewTask = false;

  // Working copy properties for editing
  editableTaskTitle: string = '';
  editableTaskDescription: string = '';
  editableTaskPriority: Priority = 'medium';
  editableTaskStartDate: string = '';
  editableCardStartAndEndDate: string = '';
  editableTaskEndDate: string = '';
  regeneratePrompt: string = '';
  // Dropdown state
  openDropdownId: string | null = null;

  priorities = [
    { name: 'High', value: 'High' },
    { name: 'Medium', value: 'Medium' },
    { name: 'Low', value: 'Low' },
  ];

  constructor(private roadmapDataService: RoadmapDataService) {}

  ngOnInit(): void {
    // Subscribe to tasks and convert to GanttTask format
    this.subscription.add(
      this.roadmapDataService.tasks$.subscribe(() => {
        this.projectTasks = this.roadmapDataService.getGanttTasks();
      }),
    );

    // Close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-menu-container')) {
        this.closeAllDropdowns();
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

   onRegenerate(): void {
    console.log('Regenerate with prompt:', this.regeneratePrompt);
  }

  // Modal Methods - Centralized Add Task functionality for both views
  addNewTask(): void {
    this.isAddingNewTask = true;
    this.selectedTaskForEdit = null;
    this.clearEditableData();
    this.isEditModalOpen = true;
  }

  openEditModal(task: RoadmapTask): void {
    this.isAddingNewTask = false;
    this.selectedTaskForEdit = { ...task };
    this.setupEditableData(task);
    this.isEditModalOpen = true;
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedTaskForEdit = null;
    this.isAddingNewTask = false;
    this.clearEditableData();
  }

  updateTask(): void {
    if (this.isAddingNewTask) {
      // Add new task - handles both timeline and card view
      if (!this.editableTaskTitle.trim()) {
        alert('Please enter a task title');
        return;
      }

      if (!this.editableTaskEndDate) {
        alert('Please select an end date');
        return;
      }

      const startDate = this.editableTaskStartDate
        ? this.roadmapDataService.parseDateFromInput(this.editableTaskStartDate)
        : new Date();
      const endDate = this.roadmapDataService.parseDateFromInput(
        this.editableTaskEndDate,
      );

      // Auto-calculate quarter based on start date
      const quarter = this.roadmapDataService.getQuarterFromDate(startDate);

      this.roadmapDataService.addTask({
        task: this.editableTaskTitle.trim(),
        description:
          this.editableTaskDescription.trim() || 'No description provided',
        priority: this.editableTaskPriority,
        startDate: startDate,
        endDate: endDate,
        quarter: quarter,
      });
    } else if (this.selectedTaskForEdit) {
      // Update existing task - only for timeline view edits
      if (!this.editableTaskTitle.trim()) {
        alert('Please enter a task title');
        return;
      }

      if (!this.editableTaskEndDate) {
        alert('Please select an end date');
        return;
      }

      const startDate = this.editableTaskStartDate
        ? this.roadmapDataService.parseDateFromInput(this.editableTaskStartDate)
        : this.selectedTaskForEdit.startDate;
      const endDate = this.roadmapDataService.parseDateFromInput(
        this.editableTaskEndDate,
      );

      // Auto-update quarter based on new start date
      const quarter = this.roadmapDataService.getQuarterFromDate(startDate);

      this.roadmapDataService.updateTask(this.selectedTaskForEdit.id, {
        task: this.editableTaskTitle.trim(),
        description:
          this.editableTaskDescription.trim() || 'No description provided',
        priority: this.editableTaskPriority,
        startDate: startDate,
        endDate: endDate,
        quarter: quarter,
      });
    }
    this.closeEditModal();
  }

  deleteTask(taskId: string): void {
    this.roadmapDataService.deleteTask(taskId);
  }

  private clearEditableData(): void {
    this.editableTaskTitle = '';
    this.editableTaskDescription = '';
    this.editableTaskPriority = 'medium';
    this.editableTaskStartDate = '';
    this.editableTaskEndDate = '';
    this.regeneratePrompt = '';
  }

  private setupEditableData(task: RoadmapTask): void {
    this.editableTaskTitle = task.task;
    this.editableTaskDescription = task.description;
    this.editableTaskPriority = task.priority;
    this.editableTaskStartDate = this.roadmapDataService.formatDateForInput(
      task.startDate,
    );
    this.editableTaskEndDate = this.roadmapDataService.formatDateForInput(
      task.endDate,
    );
    this.regeneratePrompt = '';
  }

  onRangeSelected(event: { start: Date; end: Date }) {
    this.editableTaskStartDate =
      this.roadmapDataService.formatDateForInput(event.start);
    this.editableTaskEndDate =
      this.roadmapDataService.formatDateForInput(event.end);
  }

  setActiveView(view: 'timeline' | 'card') {
    this.activeView = view;
  }

  // Dropdown Methods
  toggleDropdown(taskId: string, event: Event): void {
    event.stopPropagation();
    this.openDropdownId = this.openDropdownId === taskId ? null : taskId;
  }

  isDropdownOpen(taskId: string): boolean {
    return this.openDropdownId === taskId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }
}
