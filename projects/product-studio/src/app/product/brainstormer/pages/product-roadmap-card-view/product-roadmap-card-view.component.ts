import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import {
  HeadingComponent,
  IconsComponent,
  BodyTextComponent,
  InputComponent,
  DropdownComponent,
  DatepickerComponent,
} from '@awe/play-comp-library';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import {
  RoadmapDataService,
  RoadmapTask,
  Priority,
  QuarterSection,
} from '../../services/roadmap-data.service';

@Component({
  selector: 'app-product-roadmap-card-view',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    HeadingComponent,
    AweCardComponent,
    IconsComponent,
    AweModalComponent,
    BodyTextComponent,
    InputComponent,
    DropdownComponent,
    DatepickerComponent,
  ],
  templateUrl: './product-roadmap-card-view.component.html',
  styleUrl: './product-roadmap-card-view.component.scss', // Corrected property name
})
export class ProductRoadmapCardViewComponent implements OnInit, OnDestroy {
  // Event emitter to communicate with parent component for add functionality
  @Output() addTaskRequested = new EventEmitter<void>();

  // roboBallIcon: string = '/icons/robo_ball.svg'; // Not used in current HTML, but keep if needed later
  threeDotsIcon: string = 'icons/three-dot.svg';

  // Modal State - Only for editing, add functionality handled by parent
  isEditModalOpen = false;
  selectedRoadmapCardForEdit: RoadmapTask | null = null;
  // Create a working copy for editing to avoid direct mutation until save
  editableRoadmapCardTitle: string = '';
  editableRoadmapCardDescription: string = '';
  editableRoadmapCardPriority: Priority = 'medium';
  editableRoadmapCardStartDate: string = '';
  editableRoadmapCardEndDate: string = '';
  regeneratePrompt: string = '';

  openDropdownId: string | null = null;

  projectTasks: RoadmapTask[] = [];
  quarters: QuarterSection[] = [];
  private subscription = new Subscription();

  constructor(private roadmapDataService: RoadmapDataService) {}

  // Convert priorities to Option[]
  priorities = [
    { name: 'High', value: 'High' },
    { name: 'Medium', value: 'Medium' },
    { name: 'Low', value: 'Low' },
  ];

  ngOnInit(): void {
    // Subscribe to data from the service
    this.subscription.add(
      this.roadmapDataService.tasks$.subscribe((tasks) => {
        this.projectTasks = tasks;
      }),
    );

    this.subscription.add(
      this.roadmapDataService.quarters$.subscribe((quarters) => {
        this.quarters = quarters;
      }),
    );

    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-menu-container')) {
        // Check for a common parent for dropdown button & menu
        this.closeAllDropdowns();
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  initializeQuarters(): void {
    this.quarters = [
      {
        id: 'quarter-1',
        title: 'Quarter 1',
        subtitle: 'Q1 2025',
        qurterColor: '#5030E5',
        tasks: this.projectTasks.filter((task) => task.quarter === 1),
      },
      {
        id: 'quarter-2',
        title: 'Quarter 2',
        qurterColor: '#76A5EA',
        subtitle: 'Q2 2025',
        tasks: this.projectTasks.filter((task) => task.quarter === 2),
      },
      {
        id: 'quarter-3',
        title: 'Quarter 3',
        qurterColor: '#FFA500',
        subtitle: 'Q3 2025',
        tasks: this.projectTasks.filter((task) => task.quarter === 3),
      },
      {
        id: 'quarter-4',
        title: 'Quarter 4',
        qurterColor: '#8BC48A',
        subtitle: 'Q4 2025',
        tasks: this.projectTasks.filter((task) => task.quarter === 4),
      },
    ];
  }

  onRangeSelected(event: { start: Date; end: Date }) {
    this.editableRoadmapCardStartDate =
      this.roadmapDataService.formatDateForInput(event.start);
    this.editableRoadmapCardEndDate =
      this.roadmapDataService.formatDateForInput(event.end);
  }

  getSectionIds(): string[] {
    return this.quarters.map((quarter) => quarter.id);
  }

  getPriorityColor(priority: Priority): { color: string; background: string } {
    switch (priority) {
      case 'low':
        return {
          color: '#D58D49',
          background: 'rgba(223, 168, 116, 0.20)',
        };
      case 'medium':
        return {
          color: '#68B266',
          background: 'rgba(131, 194, 157, 0.20)',
        };
      case 'high':
        return {
          color: '#D58D49',
          background: 'rgba(216, 114, 125, 0.10)',
        };
      default:
        return {
          color: '#68B266',
          background: 'rgba(131, 194, 157, 0.20)',
        };
    }
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  }
  // Add this method to handle the regenerate action
  onRegenerate(): void {
    // Implement your regenerate logic here, for example:
    // You can use this.regeneratePrompt as the input prompt
    // and update the editable fields as needed.
    console.log('Regenerate with prompt:', this.regeneratePrompt);
  }

  onDrop(event: CdkDragDrop<RoadmapTask[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );

      // Update quarter assignment when moving between quarters
      const movedTask = event.container.data[event.currentIndex];
      const targetQuarterId =
        event.container.element.nativeElement.getAttribute('data-quarter-id');
      if (targetQuarterId) {
        const quarterNumber = parseInt(targetQuarterId.split('-')[1]);
        movedTask.quarter = quarterNumber;
      }
    }
  }

  addNewRoadmapCard(_quarterId: string): void {
    // Emit event to parent component to handle add functionality
    this.addTaskRequested.emit();
  }

  deleteRoadmapCard(roadmapCardId: string): void {
    this.roadmapDataService.deleteTask(roadmapCardId);
    this.closeAllDropdowns(); // Ensure dropdown closes after deletion
  }

  toggleDropdown(roadmapCardId: string, event: Event): void {
    event.stopPropagation();
    this.openDropdownId =
      this.openDropdownId === roadmapCardId ? null : roadmapCardId;
  }

  isDropdownOpen(roadmapCardId: string): boolean {
    return this.openDropdownId === roadmapCardId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  openEditModal(roadmapCard: RoadmapTask): void {
    this.selectedRoadmapCardForEdit = { ...roadmapCard }; // Edit a copy
    // Set up editable data
    this.editableRoadmapCardTitle = roadmapCard.task;
    this.editableRoadmapCardDescription = roadmapCard.description;
    this.editableRoadmapCardPriority = roadmapCard.priority;
    this.editableRoadmapCardStartDate =
      this.roadmapDataService.formatDateForInput(roadmapCard.startDate);
    this.editableRoadmapCardEndDate =
      this.roadmapDataService.formatDateForInput(roadmapCard.endDate);
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedRoadmapCardForEdit = null;
    this.editableRoadmapCardTitle = '';
    this.editableRoadmapCardDescription = '';
    this.editableRoadmapCardPriority = 'medium';
    this.editableRoadmapCardStartDate = '';
    this.editableRoadmapCardEndDate = '';
    this.regeneratePrompt = '';
  }

  updateRoadmapCard(): void {
    // Validate required fields
    if (!this.editableRoadmapCardTitle.trim()) {
      alert('Please enter a task title');
      return;
    }

    if (!this.editableRoadmapCardEndDate) {
      alert('Please select an end date');
      return;
    }

    // Only handle editing existing roadmap card - add functionality moved to parent
    if (!this.selectedRoadmapCardForEdit) return;

    const startDate = this.editableRoadmapCardStartDate
      ? this.roadmapDataService.parseDateFromInput(
          this.editableRoadmapCardStartDate,
        )
      : this.selectedRoadmapCardForEdit.startDate;
    const endDate = this.roadmapDataService.parseDateFromInput(
      this.editableRoadmapCardEndDate,
    );

    // Auto-update quarter based on new start date
    const quarter = this.roadmapDataService.getQuarterFromDate(startDate);

    this.roadmapDataService.updateTask(this.selectedRoadmapCardForEdit.id, {
      task: this.editableRoadmapCardTitle.trim(),
      description:
        this.editableRoadmapCardDescription.trim() ||
        'No description provided',
      priority: this.editableRoadmapCardPriority,
      startDate: startDate,
      endDate: endDate,
      quarter: quarter,
    });

    this.closeEditModal();
  }

  // Required for ngFor with [(ngModel)] on primitive types like string
  trackByFn(index: number, _item: any): any {
    return index;
  }

  // This method is for direct delete from card, called by (click) on delete button
  handleDeleteRoadmapCardFromCard(roadmapCardId: string) {
    this.deleteRoadmapCard(roadmapCardId);
    this.closeAllDropdowns();
  }
}
